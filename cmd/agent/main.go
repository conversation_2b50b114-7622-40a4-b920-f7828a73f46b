package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"golem-backend/internal/engine"
	"golem-backend/pkg/utils"
)

func main() {
	// 打印项目信息
	utils.NewBanner("Golem agent", "1.0.0", "Golem agent platform", "<EMAIL>").Print()

	// 创建代理引擎
	app, err := engine.NewAgentEngine()
	if err != nil {
		log.Fatalf("Failed to create agent engine: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化引擎
	if err := app.Initialize(ctx); err != nil {
		log.Fatalf("Failed to initialize engine: %v", err)
	}

	// 启动引擎
	if err := app.Start(ctx); err != nil {
		log.Fatalf("Failed to start engine: %v", err)
	}

	log.Println("Golem agent platform started successfully")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 阻塞等待信号
	sig := <-sigChan
	log.Printf("Received signal: %v, shutting down...", sig)

	// 创建关闭上下文，设置30秒超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 优雅关闭
	if err := app.Stop(shutdownCtx); err != nil {
		log.Printf("Shutdown error: %v", err)
	}

	log.Println("Golem agent platform stopped")
}
