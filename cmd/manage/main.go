package main

import (
	"fmt"
	"golem-backend/internal/engine"
	"golem-backend/pkg/utils"
	"log"
)

func main() {
	// 打印项目信息
	utils.NewBanner("Golem manage", "1.0.0", "Golem manage platform", "<EMAIL>").Print()

	app, err := engine.NewManageEngine()
	if err != nil {
		log.Fatalf("NewManageEngine err: %v", err)
	}
	var errs []error
	if err = app.RunManageHttpServer(); err != nil {
		errs = append(errs, fmt.Errorf("start manage http server error: %w", err))
	}

	if err = app.RunManageGrpcServer(); err != nil {
		errs = append(errs, fmt.Errorf("start manage grpc server error: %w", err))
	}

	if err = app.RunManageWebSocketServer(); err != nil {
		errs = append(errs, fmt.Errorf("start manage websocket server error: %w", err))
	}

	if len(errs) > 0 {
		log.Printf("manage server errs: %v", errs)
	}
}
