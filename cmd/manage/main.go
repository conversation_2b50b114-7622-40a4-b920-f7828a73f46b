package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"golem-backend/internal/engine"
	"golem-backend/pkg/utils"
)

func main() {
	// 打印项目信息
	utils.NewBanner("Golem manage", "1.0.0", "Golem manage platform", "<EMAIL>").Print()

	// 创建管理引擎
	app, err := engine.NewManageEngine()
	if err != nil {
		log.Fatalf("Failed to create manage engine: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化引擎
	if err := app.Initialize(ctx); err != nil {
		log.Fatalf("Failed to initialize engine: %v", err)
	}

	// 启动核心引擎
	if err := app.Start(ctx); err != nil {
		log.Fatalf("Failed to start core engine: %v", err)
	}

	// 启动HTTP服务器
	if err := app.RunManageHttpServer(); err != nil {
		log.Printf("Failed to start HTTP server: %v", err)
	}

	// 启动gRPC服务器
	if err := app.RunManageGrpcServer(); err != nil {
		log.Printf("Failed to start gRPC server: %v", err)
	}

	// 启动WebSocket服务器
	if err := app.RunManageWebSocketServer(); err != nil {
		log.Printf("Failed to start WebSocket server: %v", err)
	}

	log.Println("Golem manage platform started successfully")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 阻塞等待信号
	sig := <-sigChan
	log.Printf("Received signal: %v, shutting down...", sig)

	// 创建关闭上下文，设置30秒超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 优雅关闭
	if err := app.Shutdown(shutdownCtx); err != nil {
		log.Printf("Shutdown error: %v", err)
	}

	log.Println("Golem manage platform stopped")
}
