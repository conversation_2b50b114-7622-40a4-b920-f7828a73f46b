# Golem 扫描器配置文件

# ZMap 扫描器配置
zmap:
  # ZMap 可执行文件路径
  zmap_path: "/usr/bin/zmap"
  
  # 最大扫描速率 (packets/sec)
  max_rate: 10000
  
  # 带宽限制
  bandwidth: "10M"
  
  # 冷却时间 (seconds)
  cooldown_time: 8
  
  # 网络接口 (可选)
  interface: ""
  
  # 源IP地址 (可选)
  source_ip: ""

# Nmap 扫描器配置
nmap:
  # Nmap 可执行文件路径
  nmap_path: "/usr/bin/nmap"
  
  # 最大并行扫描数
  max_parallel: 100
  
  # 扫描超时时间 (minutes)
  timeout: 5
  
  # 默认扫描类型
  scan_type: "syn"
  
  # 默认时序模板 (T0-T5)
  timing: "T3"
  
  # 默认NSE脚本
  scripts:
    - "default"
    - "discovery"

# Nuclei 扫描器配置
nuclei:
  # Nuclei 可执行文件路径
  nuclei_path: "/usr/bin/nuclei"
  
  # 模板路径
  templates_path: "/opt/nuclei-templates"
  
  # 最大并发数
  max_concurrency: 25
  
  # 扫描超时时间 (minutes)
  timeout: 10
  
  # 速率限制 (requests/sec)
  rate_limit: 150
  
  # 默认严重性过滤
  severity:
    - "critical"
    - "high"
    - "medium"
  
  # 默认标签过滤
  tags:
    - "cve"
    - "rce"
    - "sqli"
    - "xss"
  
  # 排除标签
  exclude_tags:
    - "dos"
    - "fuzz"
  
  # 自定义模板列表
  templates:
    - "cves/"
    - "vulnerabilities/"
    - "exposures/"

# 扫描器优先级配置
priority:
  # 端口扫描优先级
  port_scan:
    - "zmap"
    - "nmap"
    - "default"
  
  # 服务识别优先级
  service_detect:
    - "nmap"
    - "default"
  
  # 指纹识别优先级
  fingerprint_scan:
    - "nmap"
    - "default"
  
  # 漏洞扫描优先级
  vuln_scan:
    - "nuclei"
    - "default"
  
  # 资产发现优先级
  asset_discovery:
    - "nmap"
    - "zmap"
    - "default"

# 扫描器全局配置
global:
  # 默认超时时间 (minutes)
  default_timeout: 30
  
  # 最大重试次数
  max_retries: 3
  
  # 重试间隔 (seconds)
  retry_interval: 5
  
  # 是否启用详细日志
  verbose_logging: false
  
  # 结果缓存时间 (hours)
  result_cache_ttl: 24
  
  # 临时文件目录
  temp_dir: "/tmp/golem-scans"
  
  # 最大并发任务数
  max_concurrent_tasks: 10

# 预设扫描配置
presets:
  # 快速扫描
  quick:
    zmap:
      max_rate: 1000
      bandwidth: "1M"
    nmap:
      timing: "T4"
      max_parallel: 50
    nuclei:
      max_concurrency: 10
      severity: ["critical", "high"]
  
  # 标准扫描
  standard:
    zmap:
      max_rate: 5000
      bandwidth: "5M"
    nmap:
      timing: "T3"
      max_parallel: 100
    nuclei:
      max_concurrency: 25
      severity: ["critical", "high", "medium"]
  
  # 深度扫描
  thorough:
    zmap:
      max_rate: 10000
      bandwidth: "10M"
    nmap:
      timing: "T2"
      max_parallel: 200
      scripts: ["default", "discovery", "vuln"]
    nuclei:
      max_concurrency: 50
      severity: ["critical", "high", "medium", "low"]
      templates: ["cves/", "vulnerabilities/", "exposures/", "misconfiguration/"]

# 目标配置
targets:
  # 默认端口列表
  default_ports:
    tcp:
      - 21    # FTP
      - 22    # SSH
      - 23    # Telnet
      - 25    # SMTP
      - 53    # DNS
      - 80    # HTTP
      - 110   # POP3
      - 143   # IMAP
      - 443   # HTTPS
      - 993   # IMAPS
      - 995   # POP3S
      - 1433  # MSSQL
      - 3306  # MySQL
      - 3389  # RDP
      - 5432  # PostgreSQL
      - 6379  # Redis
      - 8080  # HTTP-Alt
      - 8443  # HTTPS-Alt
    
    udp:
      - 53    # DNS
      - 67    # DHCP
      - 68    # DHCP
      - 123   # NTP
      - 161   # SNMP
      - 162   # SNMP Trap
      - 500   # IKE
      - 514   # Syslog
      - 1194  # OpenVPN
      - 4500  # IPSec NAT-T
  
  # 常用端口范围
  port_ranges:
    well_known: "1-1023"
    registered: "1024-49151"
    dynamic: "49152-65535"
    top_1000: "1-1000"
    all: "1-65535"

# 输出配置
output:
  # 结果格式
  formats:
    - "json"
    - "xml"
    - "csv"
  
  # 输出目录
  directory: "/var/log/golem/scans"
  
  # 文件命名模式
  filename_pattern: "{scanner}_{task_id}_{timestamp}"
  
  # 是否压缩输出
  compress: true
  
  # 保留天数
  retention_days: 30

# 通知配置
notifications:
  # 是否启用通知
  enabled: true
  
  # 通知条件
  conditions:
    - "scan_completed"
    - "scan_failed"
    - "critical_vulnerability_found"
    - "high_vulnerability_found"
  
  # 通知方式
  methods:
    - "webhook"
    - "email"
    - "slack"

# 性能调优
performance:
  # 内存限制 (MB)
  memory_limit: 1024
  
  # CPU限制 (cores)
  cpu_limit: 2
  
  # 网络带宽限制 (Mbps)
  bandwidth_limit: 100
  
  # 磁盘空间限制 (GB)
  disk_limit: 10
  
  # 进程优先级
  process_priority: 0

# 安全配置
security:
  # 是否启用沙箱
  sandbox_enabled: true
  
  # 允许的网络范围
  allowed_networks:
    - "10.0.0.0/8"
    - "172.16.0.0/12"
    - "192.168.0.0/16"
  
  # 禁止的网络范围
  blocked_networks:
    - "127.0.0.0/8"
    - "169.254.0.0/16"
    - "224.0.0.0/4"
  
  # 最大扫描目标数
  max_targets: 1000
  
  # 扫描频率限制 (scans/hour)
  rate_limit: 100
