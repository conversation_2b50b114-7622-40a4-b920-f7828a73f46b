version: 1.0.0

# Golem Core 应用配置
application:
  mode: "debug"  # debug, release
  timeout: 30    # 单位：秒
  auth:
    jwt_secret: "golem_secret@jfghd4"
    jwt_expire: 86400  # 24小时，单位：秒

  # 管理平台配置
  manage:
    name: "Golem Manage"
    server:
      http_listen_port: 10013

  # 测绘中心配置
  mapping:
    name: "Golem Mapping"
    server:
      http_listen_port: 10016

  # 代理平台配置
  agent:
    name: "Golem Agent"

# 数据库配置
database:
  driver: "mongodb"
  host: "***************"
  port: 27017
  username: "admin"
  password: "golem5011"
  database: "GOLEM_MANAGE_DEV"
  max_idle: 10
  max_open: 100

# 缓存配置
redis:
  enable: true
  host: "***************"
  port: 6379
  password: "golem5011"
  db: 0

# 消息队列配置
rabbitmq:
  enable: true
  host: ***************
  port: 5672
  username: "admin"
  password: "golem5011"
  exchange: "golem_asm"
  queue: "golem_asm"

# 日志配置
logger:
  level: "info"
  format: "json"
  output: "stdout" # stdout, file
  file_path: "cmd/manage/logs/golem-core.log"
  max_size: 100
  max_backups: 3
  max_age: 7
