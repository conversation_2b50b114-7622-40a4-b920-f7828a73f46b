package response

import (
	"github.com/gin-gonic/gin"
	"golem-backend/pkg/utils"
	"net/http"
)

// Response 统一响应结构
type Response struct {
	Code      int         `json:"code" example:"200"`                // 响应码，0 表示成功，其他值表示失败
	Message   string      `json:"message" example:"success"`         // 响应信息
	Data      interface{} `json:"data,omitempty"`                    // 响应数据
	Timestamp int64       `json:"timestamp" example:"1139952518397"` // 响应时间戳
}

// Success 成功响应
func Success(ctx *gin.Context, message string, data interface{}) {
	if message == "" {
		message = "success"
	}
	ctx.JSON(http.StatusOK, Response{
		Code:      0,
		Message:   message,
		Data:      data,
		Timestamp: utils.GetCurrentMilliUnix(),
	})
}

// Error 错误响应
func Error(ctx *gin.Context, httpCode, code int, message string, err ...error) {
	if message == "" {
		message = "error"
	}
	ctx.JSON(httpCode, Response{
		Code:    code,
		Message: message,
		Data: gin.H{
			"error": err,
		},
		Timestamp: utils.GetCurrentMilliUnix(),
	})
}
