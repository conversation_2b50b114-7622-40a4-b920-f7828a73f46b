package logger

import (
	"github.com/sirupsen/logrus"
	"golem-backend/pkg/config"
)

type Logger interface {
	Trace(args ...interface{})
	Debug(args ...interface{})
	Print(args ...interface{})
	Info(args ...interface{})
	Warn(args ...interface{})
	Warning(args ...interface{})
	Error(args ...interface{})
	Panic(args ...interface{})
	Fatal(args ...interface{})
}

type logger struct {
	logger *logrus.Logger
}

// NewLogger 初始化日志
func NewLogger(cfg *config.Logger) Logger {
	log := logrus.New()

	return &logger{log}
}

// Trace logs a message at level Trace on the standard logger.
func (l *logger) Trace(args ...interface{}) {
	l.logger.Trace(args...)
}

// Debug logs a message at level Debug on the standard logger.
func (l *logger) Debug(args ...interface{}) {
	l.logger.Debug(args...)
}

// Print logs a message at level Info on the standard logger.
func (l *logger) Print(args ...interface{}) {
	l.logger.Print(args...)
}

// Info logs a message at level Info on the standard logger.
func (l *logger) Info(args ...interface{}) {
	l.logger.Info(args...)
}

// Warn logs a message at level Warn on the standard logger.
func (l *logger) Warn(args ...interface{}) {
	l.logger.Warn(args...)
}

// Warning logs a message at level Warn on the standard logger.
func (l *logger) Warning(args ...interface{}) {
	l.logger.Warning(args...)
}

// Error logs a message at level Error on the standard logger.
func (l *logger) Error(args ...interface{}) {
	l.logger.Error(args...)
}

// Panic logs a message at level Panic on the standard logger.
func (l *logger) Panic(args ...interface{}) {
	l.logger.Panic(args...)
}

// Fatal logs a message at level Fatal on the standard logger then the process will exit with status set to 1.
func (l *logger) Fatal(args ...interface{}) {
	l.logger.Fatal(args...)
}
