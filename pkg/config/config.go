package config

import (
	"fmt"
	"github.com/spf13/viper"
	"strings"
	"time"
)

// Config 全局配置
type Config struct {
	Application Application `mapstructure:"application"`
	Database    Database    `mapstructure:"database"`
	Redis       Redis       `mapstructure:"redis"`
	Rabbitmq    Rabbitmq    `mapstructure:"rabbitmq"`
	Logger      Logger      `mapstructure:"logger"`
}

// Application 应用配置
type Application struct {
	Mode    string        `mapstructure:"mode"`
	Timeout time.Duration `mapstructure:"timeout"`
	Auth    struct {
		JWTSecret string `mapstructure:"jwt_secret"`
		JWTExpire int    `mapstructure:"jwt_expire"`
	} `mapstructure:"auth"`
	Manage Manage `mapstructure:"manage"`
}

// Manage 管理平台配置
type Manage struct {
	Name   string `mapstructure:"name"`
	Server struct {
		HTTPListenPort      int `mapstructure:"http_listen_port"`
		GrpcListenPort      int `mapstructure:"grpc_listen_port"`
		WebsocketListenPort int `mapstructure:"websocket_listen_port"`
	} `mapstructure:"server"`
}

// Database 数据库配置
type Database struct {
	Driver   string `mapstructure:"driver"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	MaxIdle  int    `mapstructure:"max_idle"`
	MaxOpen  int    `mapstructure:"max_open"`
}

// Redis 缓存配置
type Redis struct {
	Enable   bool   `mapstructure:"enable"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Db       int    `mapstructure:"db"`
}

// Rabbitmq 消息队列配置
type Rabbitmq struct {
	Enable   bool   `mapstructure:"enable"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Exchange string `mapstructure:"exchange"`
	Queue    string `mapstructure:"queue"`
}

// Logger 日志配置
type Logger struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
}

// LoadConfig 加载配置
func LoadConfig(configFile string) (*Config, error) {
	// 设置配置文件
	viper.SetConfigFile(configFile)

	// 设置配置文件类型
	viper.SetConfigType("yaml")

	// 设置环境变量前缀
	viper.SetEnvPrefix("GOLEM")

	// 设置环境变量键名替换器
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 自动读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config: %w", err)
	}

	// 解析配置文件
	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}
