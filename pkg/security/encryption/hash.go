package encryption

import "golang.org/x/crypto/bcrypt"

const (
	// DefaultBcryptCost 默认加密成本
	DefaultBcryptCost = 12
)

// HashPassword 哈希密码
func HashPassword(password string, cost ...int) (string, error) {
	// 设置加密成本
	bcryptCost := DefaultBcryptCost
	if len(cost) > 0 {
		bcryptCost = cost[0]
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcryptCost)
	if err != nil {
		return "", err
	}

	return string(hashedPassword), nil
}

// CheckPasswordHash 检查密码哈希
func CheckPasswordHash(password, hashedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}
