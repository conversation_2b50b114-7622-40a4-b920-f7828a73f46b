package errors

import (
	"fmt"
	"net/http"
)

// CustomError 自定义错误类型
type CustomError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Type    string `json:"type"`
}

func (e *CustomError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// 错误类型常量
const (
	TypeValidation     = "VALIDATION_ERROR"
	TypeNotFound       = "NOT_FOUND"
	TypeAuthentication = "AUTHENTICATION_ERROR"
	TypeAuthorization  = "AUTHORIZATION_ERROR"
	TypeInternal       = "INTERNAL_ERROR"
	TypeConflict       = "CONFLICT_ERROR"
)

// NewValidationError 创建验证错误
func NewValidationError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusBadRequest,
		Message: message,
		Type:    TypeValidation,
	}
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusNotFound,
		Message: message,
		Type:    TypeNotFound,
	}
}

// NewAuthenticationError 创建认证错误
func NewAuthenticationError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusUnauthorized,
		Message: message,
		Type:    TypeAuthentication,
	}
}

// NewAuthorizationError 创建授权错误
func NewAuthorizationError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusForbidden,
		Message: message,
		Type:    TypeAuthorization,
	}
}

// NewInternalError 创建内部错误
func NewInternalError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusInternalServerError,
		Message: message,
		Type:    TypeInternal,
	}
}

// NewConflictError 创建冲突错误
func NewConflictError(message string) *CustomError {
	return &CustomError{
		Code:    http.StatusConflict,
		Message: message,
		Type:    TypeConflict,
	}
}
