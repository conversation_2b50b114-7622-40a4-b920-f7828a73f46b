package rabbitmq

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/rabbitmq/amqp091-go"
	"golem-backend/pkg/config"
	"log"
	"time"
)

// Client RabbitMQ 客户端
type Client struct {
	conn    *amqp091.Connection
	channel *amqp091.Channel
}

// NewClient 创建 RabbitMQ 客户端
func NewClient(cfg *config.Rabbitmq) (*Client, error) {
	// 连接 RabbitMQ
	conn, err := amqp091.Dial(fmt.Sprintf("amqp://%s:%s@%s:%d", cfg.Username, cfg.Password, cfg.Host, cfg.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to rabbitmq: %w", err)
	}

	// 创建信道
	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to create a channel: %w", err)
	}

	// 声明交换机（direct类型）
	err = channel.ExchangeDeclare(
		cfg.Exchange, // 交换机名称
		"direct",     // 交换机类型
		true,         // 持久化
		false,        // 自动删除
		false,        // 内部使用
		false,        // 无等待
		nil,          // 参数
	)
	if err != nil {
		channel.Close()
		conn.Close()
		return nil, fmt.Errorf("failed to declare exchange: %w", err)
	}

	// 声明队列
	_, err = channel.QueueDeclare(
		cfg.Queue, // 队列名称
		true,      // 持久化
		false,     // 自动删除
		false,     // 独占
		false,     // 无等待
		nil,       // 参数
	)
	if err != nil {
		channel.Close()
		conn.Close()
		return nil, fmt.Errorf("failed to declare queue: %w", err)
	}

	// 绑定队列到交换机
	err = channel.QueueBind(
		cfg.Queue,    // 队列名称
		cfg.Queue,    // 路由键
		cfg.Exchange, // 交换机名称
		false,        // 无等待
		nil,          // 参数
	)
	if err != nil {
		channel.Close()
		conn.Close()
		return nil, fmt.Errorf("failed to bind queue: %w", err)
	}

	// 返回 RabbitMQ 客户端
	return &Client{conn: conn, channel: channel}, nil
}

// Publish 发布任务到RabbitMQ
func (c *Client) Publish(ctx context.Context, routingKey string, task interface{}) error {
	// 序列化任务
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}

	// 发布消息
	err = c.channel.PublishWithContext(
		ctx,
		"golem_asm", // 交换机
		routingKey,  // 路由键
		false,       // 强制
		false,       // 立即
		amqp091.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp091.Persistent, // 持久化
			Timestamp:    time.Now(),
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish task: %w", err)
	}

	return nil
}

// Subscribe 订阅RabbitMQ任务
func (c *Client) Subscribe(ctx context.Context, queueName string, handler func([]byte) error) error {
	// 设置消费者
	msgs, err := c.channel.Consume(
		queueName, // 队列名称
		"",        // 消费者标签（空为自动生成）
		false,     // 非自动确认
		false,     // 非独占
		false,     // 无本地
		false,     // 无等待
		nil,       // 参数
	)
	if err != nil {
		return fmt.Errorf("failed to start consumer: %w", err)
	}

	// 并发处理消息
	go func() {
		for msg := range msgs {
			select {
			case <-ctx.Done():
				log.Println("Consumer stopped")
				return
			default:
				if err := handler(msg.Body); err != nil {
					log.Println("Failed to process task")
					// 重试机制（示例：NACK消息）
					msg.Nack(false, true)
					continue
				}
				// 确认消息
				if err := msg.Ack(false); err != nil {
					log.Println("Failed to acknowledge message")
				}
			}
		}
	}()

	log.Println("Started RabbitMQ consumer")
	// 等待取消
	<-ctx.Done()
	return nil
}

// Close 关闭 RabbitMQ 连接
func (c *Client) Close() error {
	if c.channel != nil {
		if err := c.channel.Close(); err != nil {
			return fmt.Errorf("failed to close channel: %w", err)
		}
	}
	if c.conn != nil {
		if err := c.conn.Close(); err != nil {
			return fmt.Errorf("failed to close connection: %w", err)
		}
	}
	log.Println("RabbitMQ connection closed")
	return c.conn.Close()
}
