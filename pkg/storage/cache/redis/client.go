package redis

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"golem-backend/pkg/config"
)

// Client Redis 客户端
type Client struct {
	*redis.Client
	ctx context.Context
}

// NewClient 创建 Redis 客户端
func NewClient(cfg *config.Redis) (*Client, error) {
	// 设置 Redis 客户端
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.Db,
	})

	ctx := context.Background()

	// 检查 Redis 连接是否正常
	if _, err := client.Ping(ctx).Result(); err != nil {
		return nil, fmt.Errorf("failed to ping redis: %w", err)
	}

	// 返回 Redis 客户端
	return &Client{client, ctx}, nil
}

// Exists 检查键是否存在
func (c *Client) Exists(key string) (bool, error) {
	result, err := c.Client.Exists(c.ctx, key).Result()
	return result > 0, err
}

// Health 检查 Redis 健康状态
func (c *Client) Health() error {
	_, err := c.Ping(c.ctx).Result()
	return err
}

// Close 关闭 Redis 连接
func (c *Client) Close() error {
	return c.Client.Close()
}
