package mongodb

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"golem-backend/pkg/config"
	"time"
)

// Client MongoDB 客户端
type Client struct {
	*mongo.Client
	*mongo.Database
	ctx context.Context
}

// NewClient 创建 MongoDB 客户端
func NewClient(cfg *config.Database) (*Client, error) {
	// 设置连接超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 设置 MongoDB 连接选项
	clientOptions := options.Client().ApplyURI(fmt.Sprintf("mongodb://%s:%s@%s:%d", cfg.Username, cfg.Password, cfg.Host, cfg.Port))

	// 连接 MongoDB
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to mongodb: %w", err)
	}

	// 检查 MongoDB 连接是否正常
	if err = client.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping mongodb: %w", err)
	}

	// 设置 MongoDB 数据库
	database := client.Database(cfg.Database)

	// 返回 MongoDB 客户端
	return &Client{client, database, ctx}, nil
}

// Close 关闭 MongoDB 连接
func (c *Client) Close() error {
	return c.Client.Disconnect(c.ctx)
}
