package utils

import (
	"fmt"
	"runtime"
	"strconv"
	"strings"
)

// Version 版本信息结构体
type Version struct {
	Version   string `json:"version"`
	GitCommit string `json:"git_commit"`
	BuildTime string `json:"build_time"`
	GoVersion string `json:"go_version"`
	Platform  string `json:"platform"`
	Arch      string `json:"arch"`
}

// VersionInfo 全局版本信息
var VersionInfo = Version{
	Version:   "1.0.0",
	GitCommit: "unknown",
	BuildTime: "unknown",
	GoVersion: runtime.Version(),
	Platform:  runtime.GOOS,
	Arch:      runtime.GOARCH,
}

// SetVersion 设置版本信息
func SetVersion(version, gitCommit, buildTime string) {
	VersionInfo.Version = version
	VersionInfo.GitCommit = gitCommit
	VersionInfo.BuildTime = buildTime
}

// GetVersion 获取版本号
func GetVersion() string {
	return VersionInfo.Version
}

// GetGitCommit 获取Git提交哈希
func GetGitCommit() string {
	return VersionInfo.GitCommit
}

// GetBuildTime 获取构建时间
func GetBuildTime() string {
	return VersionInfo.BuildTime
}

// GetGoVersion 获取Go版本
func GetGoVersion() string {
	return VersionInfo.GoVersion
}

// GetPlatform 获取平台信息
func GetPlatform() string {
	return fmt.Sprintf("%s/%s", VersionInfo.Platform, VersionInfo.Arch)
}

// GetFullVersion 获取完整版本信息
func GetFullVersion() string {
	return fmt.Sprintf("%s (commit: %s, built: %s, go: %s, platform: %s/%s)",
		VersionInfo.Version,
		VersionInfo.GitCommit,
		VersionInfo.BuildTime,
		VersionInfo.GoVersion,
		VersionInfo.Platform,
		VersionInfo.Arch,
	)
}

// GetVersionInfo 获取版本信息结构体
func GetVersionInfo() Version {
	return VersionInfo
}

// CompareVersion 比较版本号
// 返回值: -1 表示 v1 < v2, 0 表示 v1 == v2, 1 表示 v1 > v2
func CompareVersion(v1, v2 string) int {
	// 移除 'v' 前缀
	v1 = strings.TrimPrefix(v1, "v")
	v2 = strings.TrimPrefix(v2, "v")

	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	// 补齐长度
	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for len(parts1) < maxLen {
		parts1 = append(parts1, "0")
	}
	for len(parts2) < maxLen {
		parts2 = append(parts2, "0")
	}

	// 逐个比较
	for i := 0; i < maxLen; i++ {
		num1, err1 := strconv.Atoi(parts1[i])
		num2, err2 := strconv.Atoi(parts2[i])

		// 如果转换失败，按字符串比较
		if err1 != nil || err2 != nil {
			if parts1[i] < parts2[i] {
				return -1
			} else if parts1[i] > parts2[i] {
				return 1
			}
			continue
		}

		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	return 0
}

// IsNewerVersion 检查版本是否更新
func IsNewerVersion(current, target string) bool {
	return CompareVersion(target, current) > 0
}

// IsValidVersion 检查版本号格式是否有效
func IsValidVersion(version string) bool {
	// 移除 'v' 前缀
	version = strings.TrimPrefix(version, "v")

	parts := strings.Split(version, ".")
	if len(parts) < 2 || len(parts) > 4 {
		return false
	}

	for _, part := range parts {
		if _, err := strconv.Atoi(part); err != nil {
			return false
		}
	}

	return true
}

// GetMajorVersion 获取主版本号
func GetMajorVersion(version string) (int, error) {
	version = strings.TrimPrefix(version, "v")
	parts := strings.Split(version, ".")
	if len(parts) == 0 {
		return 0, fmt.Errorf("invalid version format")
	}
	return strconv.Atoi(parts[0])
}

// GetMinorVersion 获取次版本号
func GetMinorVersion(version string) (int, error) {
	version = strings.TrimPrefix(version, "v")
	parts := strings.Split(version, ".")
	if len(parts) < 2 {
		return 0, fmt.Errorf("invalid version format")
	}
	return strconv.Atoi(parts[1])
}

// GetPatchVersion 获取补丁版本号
func GetPatchVersion(version string) (int, error) {
	version = strings.TrimPrefix(version, "v")
	parts := strings.Split(version, ".")
	if len(parts) < 3 {
		return 0, nil // 如果没有补丁版本号，返回0
	}
	return strconv.Atoi(parts[2])
}

// IncrementVersion 递增版本号
func IncrementVersion(version string, versionType string) (string, error) {
	version = strings.TrimPrefix(version, "v")
	parts := strings.Split(version, ".")

	if len(parts) < 3 {
		// 补齐到三位
		for len(parts) < 3 {
			parts = append(parts, "0")
		}
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return "", fmt.Errorf("invalid major version: %s", parts[0])
	}

	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return "", fmt.Errorf("invalid minor version: %s", parts[1])
	}

	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return "", fmt.Errorf("invalid patch version: %s", parts[2])
	}

	switch strings.ToLower(versionType) {
	case "major":
		major++
		minor = 0
		patch = 0
	case "minor":
		minor++
		patch = 0
	case "patch":
		patch++
	default:
		return "", fmt.Errorf("invalid version type: %s (must be major, minor, or patch)", versionType)
	}

	return fmt.Sprintf("%d.%d.%d", major, minor, patch), nil
}

// PrintVersionInfo 打印版本信息
func PrintVersionInfo() {
	fmt.Printf("Version: %s\n", VersionInfo.Version)
	fmt.Printf("Git Commit: %s\n", VersionInfo.GitCommit)
	fmt.Printf("Build Time: %s\n", VersionInfo.BuildTime)
	fmt.Printf("Go Version: %s\n", VersionInfo.GoVersion)
	fmt.Printf("Platform: %s/%s\n", VersionInfo.Platform, VersionInfo.Arch)
}
