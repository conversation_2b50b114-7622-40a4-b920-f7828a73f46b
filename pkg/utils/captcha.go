package utils

import "github.com/mojocn/base64Captcha"

var (
	// captcha driver
	driver = base64Captcha.NewDriverDigit(80, 240, 6, 0.7, 80)
)

// GenerateCaptcha captcha
func GenerateCaptcha() (string, string, string, error) {
	// generate captcha
	captchaId, captchaValue, captchaAnswer := driver.GenerateIdQuestionAnswer()
	captchaBs64Img, err := driver.DrawCaptcha(captchaValue)
	if err != nil {
		return "", "", "", err
	}

	// convert to base64
	captchaBs64Value := captchaBs64Img.EncodeB64string()

	// return captcha
	return captchaId, captchaBs64Value, captchaAnswer, err
}

// VerifyCaptcha captcha
func VerifyCaptcha(captchaId, captchaValue string) bool {
	return true
}
