package utils

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	Get(url string, headers map[string]string) (*HTTPResponse, error)
	Post(url string, body interface{}, headers map[string]string) (*HTTPResponse, error)
	Put(url string, body interface{}, headers map[string]string) (*HTTPResponse, error)
	Delete(url string, headers map[string]string) (*HTTPResponse, error)
	Patch(url string, body interface{}, headers map[string]string) (*HTTPResponse, error)
	Do(req *HTTPRequest) (*HTTPResponse, error)
	SetTimeout(timeout time.Duration)
	SetProxy(proxyURL string) error
	SetUserAgent(userAgent string)
	SetInsecureSkipVerify(skip bool)
}

// HTTPRequest HTTP请求结构
type HTTPRequest struct {
	Method     string            `json:"method"`
	URL        string            `json:"url"`
	Headers    map[string]string `json:"headers"`
	Body       interface{}       `json:"body"`
	Timeout    time.Duration     `json:"timeout"`
	UserAgent  string            `json:"user_agent"`
	ProxyURL   string            `json:"proxy_url"`
	SkipVerify bool              `json:"skip_verify"`
	Context    context.Context   `json:"-"`
}

// HTTPResponse HTTP响应结构
type HTTPResponse struct {
	StatusCode int                 `json:"status_code"`
	Status     string              `json:"status"`
	Headers    map[string][]string `json:"headers"`
	Body       []byte              `json:"body"`
	BodyString string              `json:"body_string"`
	Size       int64               `json:"size"`
	Duration   time.Duration       `json:"duration"`
}

// GolemHTTPClient 默认HTTP客户端实现
type GolemHTTPClient struct {
	client         *http.Client
	timeout        time.Duration
	userAgent      string
	proxyURL       string
	skipVerify     bool
	defaultHeaders map[string]string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient() HTTPClient {
	return &GolemHTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: false,
				},
			},
		},
		timeout:        30 * time.Second,
		userAgent:      "Golem-Core/1.0.0",
		defaultHeaders: make(map[string]string),
	}
}

// Get 发送GET请求
func (c *GolemHTTPClient) Get(url string, headers map[string]string) (*HTTPResponse, error) {
	return c.Do(&HTTPRequest{
		Method:  "GET",
		URL:     url,
		Headers: headers,
	})
}

// Post 发送POST请求
func (c *GolemHTTPClient) Post(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return c.Do(&HTTPRequest{
		Method:  "POST",
		URL:     url,
		Body:    body,
		Headers: headers,
	})
}

// Put 发送PUT请求
func (c *GolemHTTPClient) Put(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return c.Do(&HTTPRequest{
		Method:  "PUT",
		URL:     url,
		Body:    body,
		Headers: headers,
	})
}

// Delete 发送DELETE请求
func (c *GolemHTTPClient) Delete(url string, headers map[string]string) (*HTTPResponse, error) {
	return c.Do(&HTTPRequest{
		Method:  "DELETE",
		URL:     url,
		Headers: headers,
	})
}

// Patch 发送PATCH请求
func (c *GolemHTTPClient) Patch(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return c.Do(&HTTPRequest{
		Method:  "PATCH",
		URL:     url,
		Body:    body,
		Headers: headers,
	})
}

// Do 执行HTTP请求
func (c *GolemHTTPClient) Do(req *HTTPRequest) (*HTTPResponse, error) {
	startTime := time.Now()

	// 准备请求体
	var bodyReader io.Reader
	if req.Body != nil {
		switch v := req.Body.(type) {
		case string:
			bodyReader = strings.NewReader(v)
		case []byte:
			bodyReader = bytes.NewReader(v)
		default:
			jsonData, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal request body: %w", err)
			}
			bodyReader = bytes.NewReader(jsonData)
		}
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest(req.Method, req.URL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置上下文
	if req.Context != nil {
		httpReq = httpReq.WithContext(req.Context)
	}

	// 设置请求头
	c.setHeaders(httpReq, req)

	// 配置客户端
	client := c.configureClient(req)

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	duration := time.Since(startTime)

	return &HTTPResponse{
		StatusCode: resp.StatusCode,
		Status:     resp.Status,
		Headers:    resp.Header,
		Body:       body,
		BodyString: string(body),
		Size:       int64(len(body)),
		Duration:   duration,
	}, nil
}

// setHeaders 设置请求头
func (c *GolemHTTPClient) setHeaders(req *http.Request, httpReq *HTTPRequest) {
	// 设置默认请求头
	for k, v := range c.defaultHeaders {
		req.Header.Set(k, v)
	}

	// 设置User-Agent
	userAgent := c.userAgent
	if httpReq.UserAgent != "" {
		userAgent = httpReq.UserAgent
	}
	req.Header.Set("User-Agent", userAgent)

	// 设置Content-Type（如果有请求体且未设置）
	if httpReq.Body != nil && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	// 设置自定义请求头
	if httpReq.Headers != nil {
		for k, v := range httpReq.Headers {
			req.Header.Set(k, v)
		}
	}
}

// configureClient 配置HTTP客户端
func (c *GolemHTTPClient) configureClient(req *HTTPRequest) *http.Client {
	client := &http.Client{}

	// 设置超时
	timeout := c.timeout
	if req.Timeout > 0 {
		timeout = req.Timeout
	}
	client.Timeout = timeout

	// 配置传输层
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: c.skipVerify || req.SkipVerify,
		},
	}

	// 设置代理
	proxyURL := c.proxyURL
	if req.ProxyURL != "" {
		proxyURL = req.ProxyURL
	}
	if proxyURL != "" {
		if proxy, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(proxy)
		}
	}

	client.Transport = transport
	return client
}

// SetTimeout 设置超时时间
func (c *GolemHTTPClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.client.Timeout = timeout
}

// SetProxy 设置代理
func (c *GolemHTTPClient) SetProxy(proxyURL string) error {
	if proxyURL == "" {
		c.proxyURL = ""
		return nil
	}

	proxy, err := url.Parse(proxyURL)
	if err != nil {
		return fmt.Errorf("invalid proxy URL: %w", err)
	}

	c.proxyURL = proxyURL

	// 更新传输层配置
	if transport, ok := c.client.Transport.(*http.Transport); ok {
		transport.Proxy = http.ProxyURL(proxy)
	}

	return nil
}

// SetUserAgent 设置用户代理
func (c *GolemHTTPClient) SetUserAgent(userAgent string) {
	c.userAgent = userAgent
}

// SetInsecureSkipVerify 设置是否跳过TLS验证
func (c *GolemHTTPClient) SetInsecureSkipVerify(skip bool) {
	c.skipVerify = skip
	if transport, ok := c.client.Transport.(*http.Transport); ok {
		if transport.TLSClientConfig != nil {
			transport.TLSClientConfig.InsecureSkipVerify = skip
		}
	}
}

// SetDefaultHeader 设置默认请求头
func (c *GolemHTTPClient) SetDefaultHeader(key, value string) {
	c.defaultHeaders[key] = value
}

// RemoveDefaultHeader 移除默认请求头
func (c *GolemHTTPClient) RemoveDefaultHeader(key string) {
	delete(c.defaultHeaders, key)
}

// JSON 解析响应为JSON
func (r *HTTPResponse) JSON(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

// IsSuccess 检查响应是否成功
func (r *HTTPResponse) IsSuccess() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

// IsClientError 检查是否为客户端错误
func (r *HTTPResponse) IsClientError() bool {
	return r.StatusCode >= 400 && r.StatusCode < 500
}

// IsServerError 检查是否为服务器错误
func (r *HTTPResponse) IsServerError() bool {
	return r.StatusCode >= 500
}

// GetHeader 获取响应头
func (r *HTTPResponse) GetHeader(key string) string {
	values := r.Headers[key]
	if len(values) > 0 {
		return values[0]
	}
	return ""
}

// 便捷函数
var defaultHTTPClient = NewHTTPClient()

func Get(url string, headers map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.Get(url, headers)
}

func Post(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.Post(url, body, headers)
}

func Put(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.Put(url, body, headers)
}

func Delete(url string, headers map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.Delete(url, headers)
}

func Patch(url string, body interface{}, headers map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.Patch(url, body, headers)
}
