package utils

import "fmt"

// Banner 项目信息结构体
type Banner struct {
	Name        string
	Version     string
	Description string
	Author      string
}

// NewBanner 创建 Banner 实例
func NewBanner(name, version, description, author string) *Banner {
	return &Banner{
		Name:        name,
		Version:     version,
		Description: description,
		Author:      author,
	}
}

// Print 打印项目信息
func (b *Banner) Print() {
	fmt.Printf("\n")
	fmt.Printf("███████╗  ██████╗ ██╗     ███████╗███╗   ███╗\n")
	fmt.Printf("██╔════╝ ██╔═══██╗██║     ██╔════╝████╗ ████║     *  Name: %s\n", b.Name)
	fmt.Printf("██╔════╝ ██╔═══██╗██║     ██╔════╝████╗ ████║     *  Version: %s\n", b.Version)
	fmt.Printf("██║   ██║██║   ██║██║     ██╔══╝  ██║╚██╔╝██║     *  Description: %s\n", b.Description)
	fmt.Printf("╚██████╔╝╚██████╔╝███████╗███████╗██║ ╚═╝ ██║     *  Author: %s\n\n", b.Author)
}
