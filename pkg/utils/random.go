package utils

import (
	"crypto/rand"
	"math/big"
	"strconv"
	"time"
)

const (
	// numberRange 随机数字
	numberRange = "0123456789"
	// stringRange 随机字符串
	stringRange = "abcdefghijklmnopqrstuvwxyz"
)

// RandomString 生成随机字符串
func RandomString(length int) string {
	return randomRange(length, numberRange+stringRange)
}

// RandomNumber 生成随机数字
func RandomNumber(length int) string {
	return randomRange(length, numberRange)
}

// RandomNumberID 生成随机数字ID
func RandomNumberID(length int) string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	return timestamp + randomRange(length, numberRange)
}

// randomRange 生成指定长度的随机字符串
func randomRange(length int, alphabet string) string {
	// 分配内存创建一个指定长度的字节切片
	b := make([]byte, length)

	// 转换字符串长度为 int64 类型
	m := big.NewInt(int64(len(alphabet)))

	// 循环遍历字节切片
	for i := range b {
		// 生成一个指定范围内的随机数
		n, err := rand.Int(rand.Reader, m)
		if err != nil {
			return err.Error()
		}
		// 设置当前字节的值为随机索引对应的字符
		b[i] = alphabet[n.Int64()]
	}

	// 返回生成的随机字符串
	return string(b)
}
