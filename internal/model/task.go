package model

import (
	"time"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"   // 待执行
	TaskStatusRunning   TaskStatus = "running"   // 执行中
	TaskStatusCompleted TaskStatus = "completed" // 已完成
	TaskStatusFailed    TaskStatus = "failed"    // 失败
	TaskStatusCanceled  TaskStatus = "canceled"  // 已取消
)

// TaskType 任务类型枚举
type TaskType string

const (
	TaskTypePortScan        TaskType = "port_scan"        // 端口扫描
	TaskTypeServiceDetect   TaskType = "service_detect"   // 服务识别
	TaskTypeVulnScan        TaskType = "vuln_scan"        // 漏洞扫描
	TaskTypeFingerprintScan TaskType = "fingerprint_scan" // 指纹识别
	TaskTypeAssetDiscovery  TaskType = "asset_discovery"  // 资产发现
)

// Task 任务模型
type Task struct {
	// 任务ID
	ID string `json:"id" bson:"_id"`
	// 任务名称
	Name string `json:"name" bson:"name"`
	// 任务类型
	Type TaskType `json:"type" bson:"type"`
	// 任务状态
	Status TaskStatus `json:"status" bson:"status"`
	// 目标资产ID
	AssetID string `json:"asset_id" bson:"asset_id"`
	// 扫描节点ID
	NodeID string `json:"node_id" bson:"node_id"`
	// 任务配置
	Config map[string]interface{} `json:"config" bson:"config"`
	// 任务结果
	Result map[string]interface{} `json:"result,omitempty" bson:"result"`
	// 错误信息
	Error string `json:"error,omitempty" bson:"error"`
	// 进度百分比 (0-100)
	Progress int `json:"progress" bson:"progress"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" bson:"created_time"`
	// 开始时间
	StartTime *time.Time `json:"start_time,omitempty" bson:"start_time"`
	// 完成时间
	CompletedTime *time.Time `json:"completed_time,omitempty" bson:"completed_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" bson:"updated_time"`
}
