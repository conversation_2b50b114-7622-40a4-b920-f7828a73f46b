package model

import (
	"golem-backend/pkg/utils"
	"time"
)

// User 用户数据模型
type User struct {
	// 用户ID
	ID string `json:"id" bson:"_id"`
	// 用户名
	Username string `json:"username" bson:"username"`
	// 昵称
	Nickname string `json:"nickname" bson:"nickname"`
	// 密码
	Password string `json:"-" bson:"password"`
	// 头像
	Avatar string `json:"avatar" bson:"avatar"`
	// 邮箱
	Email string `json:"email" bson:"email"`
	// 手机号
	Phone string `json:"phone" bson:"phone"`
	// 角色
	Role string `json:"role" bson:"role"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" bson:"created_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty" bson:"updated_time"`
	// 删除时间
	DeletedTime time.Time `json:"-" bson:"deleted_time"`
}

// generateID 生成用户ID
func (u *User) generateID() {
	u.ID = utils.RandomNumberID(20)
}

// generateUsername 生成用户名
func (u *User) generateUsername() {
	u.Username = utils.RandomString(10)
}

// generatePassword 生成密码
func (u *User) generatePassword() {
	u.Password = utils.RandomString(12)
}

// generateNickname 生成昵称
func (u *User) generateNickname() {
	u.Nickname = utils.RandomString(10)
}
