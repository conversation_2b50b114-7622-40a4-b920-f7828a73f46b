package model

import (
	"time"
)

// NodeStatus 节点状态枚举
type NodeStatus string

const (
	NodeStatusOnline    NodeStatus = "online"    // 在线
	NodeStatusOffline   NodeStatus = "offline"   // 离线
	NodeStatusBusy      NodeStatus = "busy"      // 忙碌
	NodeStatusMaintain  NodeStatus = "maintain"  // 维护中
	NodeStatusError     NodeStatus = "error"     // 错误
)

// NodeType 节点类型枚举
type NodeType string

const (
	NodeTypeAgent   NodeType = "agent"   // 代理节点
	NodeTypeScanner NodeType = "scanner" // 扫描节点
	NodeTypeWorker  NodeType = "worker"  // 工作节点
)

// Node 节点模型
type Node struct {
	// 节点ID
	ID string `json:"id" bson:"_id"`
	// 节点名称
	Name string `json:"name" bson:"name"`
	// 节点类型
	Type NodeType `json:"type" bson:"type"`
	// 节点状态
	Status NodeStatus `json:"status" bson:"status"`
	// 节点IP地址
	IPAddress string `json:"ip_address" bson:"ip_address"`
	// 节点端口
	Port int `json:"port" bson:"port"`
	// 节点版本
	Version string `json:"version" bson:"version"`
	// 节点标签
	Tags []string `json:"tags" bson:"tags"`
	// 节点配置
	Config map[string]interface{} `json:"config" bson:"config"`
	// 节点能力 (支持的扫描类型)
	Capabilities []string `json:"capabilities" bson:"capabilities"`
	// 当前任务数
	CurrentTasks int `json:"current_tasks" bson:"current_tasks"`
	// 最大任务数
	MaxTasks int `json:"max_tasks" bson:"max_tasks"`
	// CPU使用率
	CPUUsage float64 `json:"cpu_usage" bson:"cpu_usage"`
	// 内存使用率
	MemoryUsage float64 `json:"memory_usage" bson:"memory_usage"`
	// 最后心跳时间
	LastHeartbeat time.Time `json:"last_heartbeat" bson:"last_heartbeat"`
	// 注册时间
	RegisterTime time.Time `json:"register_time" bson:"register_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" bson:"updated_time"`
}
