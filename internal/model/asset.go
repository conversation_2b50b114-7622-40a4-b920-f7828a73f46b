package model

import (
	"time"
)

// AssetType 资产类型枚举
type AssetType string

const (
	AssetTypeHost    AssetType = "host"    // 主机
	AssetTypeDomain  AssetType = "domain"  // 域名
	AssetTypeIP      AssetType = "ip"      // IP地址
	AssetTypeURL     AssetType = "url"     // URL
	AssetTypeNetwork AssetType = "network" // 网络段
)

// AssetStatus 资产状态枚举
type AssetStatus string

const (
	AssetStatusActive   AssetStatus = "active"   // 活跃
	AssetStatusInactive AssetStatus = "inactive" // 不活跃
	AssetStatusUnknown  AssetStatus = "unknown"  // 未知
)

// Asset 资产模型
type Asset struct {
	// 资产ID
	ID string `json:"id" bson:"_id"`
	// 资产名称
	Name string `json:"name" bson:"name"`
	// 资产类型
	Type AssetType `json:"type" bson:"type"`
	// 资产状态
	Status AssetStatus `json:"status" bson:"status"`
	// 资产值 (IP、域名、URL等)
	Value string `json:"value" bson:"value"`
	// 端口列表
	Ports []int `json:"ports" bson:"ports"`
	// 服务信息
	Services []Service `json:"services" bson:"services"`
	// 标签
	Tags []string `json:"tags" bson:"tags"`
	// 描述
	Description string `json:"description" bson:"description"`
	// 所属项目ID
	ProjectID string `json:"project_id" bson:"project_id"`
	// 风险等级
	RiskLevel string `json:"risk_level" bson:"risk_level"`
	// 最后扫描时间
	LastScanTime *time.Time `json:"last_scan_time,omitempty" bson:"last_scan_time"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" bson:"created_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" bson:"updated_time"`
}

// Service 服务信息
type Service struct {
	// 端口
	Port int `json:"port" bson:"port"`
	// 协议
	Protocol string `json:"protocol" bson:"protocol"`
	// 服务名称
	Name string `json:"name" bson:"name"`
	// 服务版本
	Version string `json:"version" bson:"version"`
	// 服务状态
	State string `json:"state" bson:"state"`
	// 服务横幅
	Banner string `json:"banner" bson:"banner"`
}
