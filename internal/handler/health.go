package handler

import (
	"github.com/gin-gonic/gin"
	"golem-backend/pkg/response"
)

type HealthHandler struct {
}

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// Health 系统健康
// @Summary 系统健康检查
// @Description 系统健康检查
// @Tags API
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=response.UserResponse} "health"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/health [get]
func (h *HealthHandler) Health(ctx *gin.Context) {
	response.Success(ctx, "health", nil)
}
