package handler

import (
	"github.com/gin-gonic/gin"
	"golem-backend/internal/service"
	"golem-backend/pkg/response"
	"net/http"
)

// CaptchaHandler 验证码处理器
type CaptchaHandler struct {
	captchaService service.CaptchaService
}

// NewCaptchaHandler 创建验证码处理器
func NewCaptchaHandler(captchaService service.CaptchaService) *CaptchaHandler {
	return &CaptchaHandler{captchaService}
}

// CreateCaptcha 获取验证码
// @Summary 生成验证码
// @Description 生成图形验证码，返回验证码ID和Base64编码的图片
// @Tags 验证码
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=CaptchaResponse} "获取验证码成功"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /captcha [get]
func (h *CaptchaHandler) CreateCaptcha(ctx *gin.Context) {
	// 调用验证码服务层 captchaService.CreateCaptcha() 方法创建验证码
	captchaId, captchaValue, err := h.captchaService.CreateCaptcha(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, -1, "获取验证码失败")
		return
	}

	// CaptchaResponse 验证码响应参数
	type CaptchaResponse struct {
		CaptchaID    string `json:"id"`
		CaptchaValue string `json:"value"`
	}

	// 验证码响应结构体
	result := CaptchaResponse{
		CaptchaID:    captchaId,
		CaptchaValue: captchaValue,
	}

	// 返回验证码
	response.Success(ctx, "获取验证码成功", result)
}

// VerifyCaptcha 验证验证码
func (h *CaptchaHandler) VerifyCaptcha(ctx *gin.Context) {}
