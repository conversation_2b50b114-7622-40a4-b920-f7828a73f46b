package handler

import (
	"github.com/gin-gonic/gin"
	"golem-backend/internal/service"
	"golem-backend/pkg/response"
	"net/http"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService service.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetUserByID 根据用户ID获取用户
// @Summary 获取用户
// @Description 根据用户ID获取用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response "获取成功"
// @Failure 404 {object} response.Response "用户不存在"
// @Router /api/v1/users/{id} [get]
func (h *UserHandler) GetUserByID(ctx *gin.Context) {}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body request.CreateUserRequest true "用户信息"
// @Success 201 {object} response.Response{data=response.UserResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 409 {object} response.Response "用户已存在"
// @Router /api/v1/users [post]
func (h *UserHandler) CreateUser(ctx *gin.Context) {
	// 绑定请求参数
	var req struct {
		Username string `json:"username"`
	}

	// 校验请求参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, -1, err.Error())
		return
	}

	// 调用用户服务层 userService.CreateUser() 方法创建用户
	err := h.userService.CreateUser(ctx.Request.Context(), nil)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, -1, err.Error())
		return
	}

	// 返回成功响应
	response.Success(ctx, "创建成功", nil)
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body request.UpdateUser true "用户信息"
// @Success 201 {object} response.Response{data=response.UserResponse} "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Router /api/v1/users [put]
func (h *UserHandler) UpdateUser(ctx *gin.Context) {
	// 绑定请求参数
	var req struct {
		Username string `json:"username"`
	}

	// 校验请求参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, -1, err.Error())
		return
	}

	// 调用用户服务层 userService.UpdateUser() 方法更新用户
	err := h.userService.UpdateUser(ctx.Request.Context(), nil)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, -1, err.Error())
		return
	}

	// 返回成功响应
	response.Success(ctx, "更新成功", nil)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 软删除指定用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 404 {object} response.Response "用户不存在"
// @Router /api/v1/users/{id} [delete]
func (h *UserHandler) DeleteUser(ctx *gin.Context) {
	// 绑定请求参数
	id := ctx.Param("id")

	// 校验参数
	if ctx.ShouldBindQuery(&id) != nil {
		response.Error(ctx, http.StatusBadRequest, -1, "无效的用户ID")
		return
	}

	// 调用用户服务层 userService.DeleteUser() 方法删除用户
	err := h.userService.DeleteUser(ctx.Request.Context(), id)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, -1, err.Error())
		return
	}

	// 返回成功响应
	response.Success(ctx, "删除成功", nil)
}
