package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// Scheduler 调度器接口
type Scheduler interface {
	// Start 启动调度器
	Start(ctx context.Context) error

	// Stop 停止调度器
	Stop(ctx context.Context) error

	// ScheduleTask 调度任务
	ScheduleTask(ctx context.Context, task *model.Task) error

	// CancelTask 取消任务
	CancelTask(ctx context.Context, taskID string) error

	// GetTask 获取任务
	GetTask(ctx context.Context, taskID string) (*model.Task, error)

	// ListTasks 列出任务
	ListTasks(ctx context.Context, status model.TaskStatus) ([]*model.Task, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(ctx context.Context, taskID string, status model.TaskStatus) error
}

// TaskScheduler 任务调度器实现
type TaskScheduler struct {
	mu       sync.RWMutex
	tasks    map[string]*model.Task
	queue    chan *model.Task
	workers  int
	logger   logger.Logger
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// NewScheduler 创建调度器
func NewScheduler(log logger.Logger) Scheduler {
	return &TaskScheduler{
		tasks:   make(map[string]*model.Task),
		queue:   make(chan *model.Task, 1000),
		workers: 10, // 默认10个工作协程
		logger:  log,
	}
}

// Start 启动调度器
func (ts *TaskScheduler) Start(ctx context.Context) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.ctx != nil {
		return fmt.Errorf("scheduler already started")
	}

	ts.ctx, ts.cancel = context.WithCancel(ctx)

	// 启动工作协程
	for i := 0; i < ts.workers; i++ {
		ts.wg.Add(1)
		go ts.worker(i)
	}

	ts.logger.Info(fmt.Sprintf("Task scheduler started with %d workers", ts.workers))
	return nil
}

// Stop 停止调度器
func (ts *TaskScheduler) Stop(ctx context.Context) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.cancel == nil {
		return nil
	}

	ts.cancel()
	close(ts.queue)

	// 等待所有工作协程完成
	ts.wg.Wait()

	ts.ctx = nil
	ts.cancel = nil

	ts.logger.Info("Task scheduler stopped")
	return nil
}

// ScheduleTask 调度任务
func (ts *TaskScheduler) ScheduleTask(ctx context.Context, task *model.Task) error {
	if task == nil {
		return fmt.Errorf("task cannot be nil")
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	// 检查任务是否已存在
	if _, exists := ts.tasks[task.ID]; exists {
		return fmt.Errorf("task %s already exists", task.ID)
	}

	// 设置任务状态
	task.Status = model.TaskStatusPending
	task.CreatedTime = time.Now()
	task.UpdatedTime = time.Now()

	// 存储任务
	ts.tasks[task.ID] = task

	// 将任务加入队列
	select {
	case ts.queue <- task:
		ts.logger.Info(fmt.Sprintf("Task scheduled: %s", task.ID))
		return nil
	default:
		delete(ts.tasks, task.ID)
		return fmt.Errorf("task queue full")
	}
}

// CancelTask 取消任务
func (ts *TaskScheduler) CancelTask(ctx context.Context, taskID string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	task, exists := ts.tasks[taskID]
	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	// 只能取消待执行或运行中的任务
	if task.Status != model.TaskStatusPending && task.Status != model.TaskStatusRunning {
		return fmt.Errorf("cannot cancel task %s with status %s", taskID, task.Status)
	}

	task.Status = model.TaskStatusCanceled
	task.UpdatedTime = time.Now()

	ts.logger.Info(fmt.Sprintf("Task canceled: %s", taskID))
	return nil
}

// GetTask 获取任务
func (ts *TaskScheduler) GetTask(ctx context.Context, taskID string) (*model.Task, error) {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	task, exists := ts.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return task, nil
}

// ListTasks 列出任务
func (ts *TaskScheduler) ListTasks(ctx context.Context, status model.TaskStatus) ([]*model.Task, error) {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	var tasks []*model.Task
	for _, task := range ts.tasks {
		if status == "" || task.Status == status {
			tasks = append(tasks, task)
		}
	}

	return tasks, nil
}

// UpdateTaskStatus 更新任务状态
func (ts *TaskScheduler) UpdateTaskStatus(ctx context.Context, taskID string, status model.TaskStatus) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	task, exists := ts.tasks[taskID]
	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.Status = status
	task.UpdatedTime = time.Now()

	if status == model.TaskStatusRunning && task.StartTime == nil {
		startTime := time.Now()
		task.StartTime = &startTime
	}

	if status == model.TaskStatusCompleted || status == model.TaskStatusFailed {
		completedTime := time.Now()
		task.CompletedTime = &completedTime
	}

	ts.logger.Debug(fmt.Sprintf("Task %s status updated to %s", taskID, status))
	return nil
}

// worker 工作协程
func (ts *TaskScheduler) worker(id int) {
	defer ts.wg.Done()

	ts.logger.Debug(fmt.Sprintf("Scheduler worker %d started", id))

	for {
		select {
		case task, ok := <-ts.queue:
			if !ok {
				ts.logger.Debug(fmt.Sprintf("Scheduler worker %d stopped", id))
				return
			}

			ts.processTask(task, id)

		case <-ts.ctx.Done():
			ts.logger.Debug(fmt.Sprintf("Scheduler worker %d stopped due to context cancellation", id))
			return
		}
	}
}

// processTask 处理任务
func (ts *TaskScheduler) processTask(task *model.Task, workerID int) {
	ts.logger.Debug(fmt.Sprintf("Worker %d processing task: %s", workerID, task.ID))

	// 更新任务状态为运行中
	ts.UpdateTaskStatus(ts.ctx, task.ID, model.TaskStatusRunning)

	// 这里应该将任务分发给具体的执行器
	// 目前只是模拟处理过程
	time.Sleep(time.Second) // 模拟处理时间

	// 模拟任务完成
	ts.UpdateTaskStatus(ts.ctx, task.ID, model.TaskStatusCompleted)

	ts.logger.Info(fmt.Sprintf("Task completed: %s", task.ID))
}
