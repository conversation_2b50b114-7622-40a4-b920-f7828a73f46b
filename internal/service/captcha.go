package service

import (
	"context"
	"golem-backend/internal/consts"
	"golem-backend/internal/repository"
	"golem-backend/pkg/utils"
)

// CaptchaService 验证码服务接口
type CaptchaService interface {
	// CreateCaptcha 创建验证码
	CreateCaptcha(ctx context.Context) (string, string, error)

	// VerifyCaptcha 验证验证码
	VerifyCaptcha(ctx context.Context, key, value string) bool
}

// captchaService 验证码服务实现
type captchaService struct {
	captchaRepo repository.CaptchaRepository
}

// NewCaptchaService 创建验证码服务
func NewCaptchaService(captchaRepo repository.CaptchaRepository) CaptchaService {
	return &captchaService{captchaRepo}
}

// CreateCaptcha 创建验证码
func (s *captchaService) CreateCaptcha(ctx context.Context) (string, string, error) {
	// 调用验证码工具生成验证码
	captchaID, captchaBs64Value, captchaAnswer, err := utils.GenerateCaptcha()
	if err != nil {
		return "", "", err
	}

	// 验证码缓存键
	captchaCacheKey := consts.CaptchaCacheKey + ":" + captchaID

	// 验证码过期时间
	captchaExpire := consts.CaptchaCacheExpire

	// 保存验证码
	if err = s.captchaRepo.SaveCaptcha(ctx, captchaCacheKey, captchaAnswer, captchaExpire); err != nil {
		return "", "", err
	}

	return captchaID, captchaBs64Value, nil
}

// VerifyCaptcha 验证验证码
func (s *captchaService) VerifyCaptcha(ctx context.Context, key, value string) bool {
	return true
}
