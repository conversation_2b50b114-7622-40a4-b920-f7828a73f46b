package service

import (
	"context"
	"golem-backend/internal/model"
	"golem-backend/internal/repository"
	"golem-backend/pkg/logger"
)

// UserService 用户服务接口
type UserService interface {
	// GetUserByID 根据用户ID获取用户
	GetUserByID(ctx context.Context, id string) (*model.User, error)

	// GetUserByUsername 根据用户名获取用户
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, user *model.User) error

	// UpdateUser 更新用户
	UpdateUser(ctx context.Context, user *model.User) error

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, id string) error
}

// userService 用户服务实现
type userService struct {
	logger   *logger.Logger
	userRepo *repository.UserRepository
}

// NewUserService 创建用户服务
func NewUserService(logger *logger.Logger, userRepo *repository.UserRepository) UserService {
	return &userService{
		logger:   logger,
		userRepo: userRepo,
	}
}

func (s *userService) GetUserByID(ctx context.Context, id string) (*model.User, error) {
	// TODO implement me
	panic("implement me")
}

func (s *userService) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	// TODO implement me
	panic("implement me")
}

func (s *userService) CreateUser(ctx context.Context, user *model.User) error {
	// TODO implement me
	panic("implement me")
}

func (s *userService) UpdateUser(ctx context.Context, user *model.User) error {
	// TODO implement me
	panic("implement me")
}

func (s *userService) DeleteUser(ctx context.Context, id string) error {
	// TODO implement me
	panic("implement me")
}
