package registry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// Registry 注册中心接口
type Registry interface {
	// RegisterNode 注册节点
	RegisterNode(ctx context.Context, node *model.Node) error

	// UnregisterNode 注销节点
	UnregisterNode(ctx context.Context, nodeID string) error

	// GetNode 获取节点信息
	GetNode(ctx context.Context, nodeID string) (*model.Node, error)

	// ListNodes 列出所有节点
	ListNodes(ctx context.Context) ([]*model.Node, error)

	// ListNodesByType 根据类型列出节点
	ListNodesByType(ctx context.Context, nodeType model.NodeType) ([]*model.Node, error)

	// ListNodesByStatus 根据状态列出节点
	ListNodesByStatus(ctx context.Context, status model.NodeStatus) ([]*model.Node, error)

	// UpdateNodeStatus 更新节点状态
	UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error

	// Heartbeat 节点心跳
	Heartbeat(ctx context.Context, nodeID string) error

	// WatchNodes 监听节点变化
	WatchNodes(ctx context.Context) (<-chan *NodeEvent, error)
}

// NodeEvent 节点事件
type NodeEvent struct {
	Type string      `json:"type"` // register, unregister, update, heartbeat
	Node *model.Node `json:"node"`
	Time time.Time   `json:"time"`
}

// NodeRegistry 节点注册中心实现
type NodeRegistry struct {
	mu        sync.RWMutex
	nodes     map[string]*model.Node
	watchers  []chan *NodeEvent
	logger    logger.Logger
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

// NewRegistry 创建注册中心
func NewRegistry(log logger.Logger) Registry {
	return &NodeRegistry{
		nodes:    make(map[string]*model.Node),
		watchers: make([]chan *NodeEvent, 0),
		logger:   log,
	}
}

// Start 启动注册中心
func (nr *NodeRegistry) Start(ctx context.Context) error {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	if nr.ctx != nil {
		return fmt.Errorf("registry already started")
	}

	nr.ctx, nr.cancel = context.WithCancel(ctx)

	// 启动健康检查协程
	nr.wg.Add(1)
	go nr.healthCheckLoop()

	nr.logger.Info("Node registry started")
	return nil
}

// Stop 停止注册中心
func (nr *NodeRegistry) Stop(ctx context.Context) error {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	if nr.cancel == nil {
		return nil
	}

	nr.cancel()

	// 关闭所有监听器
	for _, watcher := range nr.watchers {
		close(watcher)
	}
	nr.watchers = nil

	// 等待协程完成
	nr.wg.Wait()

	nr.ctx = nil
	nr.cancel = nil

	nr.logger.Info("Node registry stopped")
	return nil
}

// RegisterNode 注册节点
func (nr *NodeRegistry) RegisterNode(ctx context.Context, node *model.Node) error {
	if node == nil {
		return fmt.Errorf("node cannot be nil")
	}

	if node.ID == "" {
		return fmt.Errorf("node ID cannot be empty")
	}

	nr.mu.Lock()
	defer nr.mu.Unlock()

	// 检查节点是否已存在
	if _, exists := nr.nodes[node.ID]; exists {
		return fmt.Errorf("node %s already registered", node.ID)
	}

	// 设置注册时间
	node.RegisterTime = time.Now()
	node.LastHeartbeat = time.Now()
	node.UpdatedTime = time.Now()
	node.Status = model.NodeStatusOnline

	// 存储节点
	nr.nodes[node.ID] = node

	// 发送事件
	nr.sendEvent(&NodeEvent{
		Type: "register",
		Node: node,
		Time: time.Now(),
	})

	nr.logger.Info(fmt.Sprintf("Node registered: %s (%s)", node.ID, node.Name))
	return nil
}

// UnregisterNode 注销节点
func (nr *NodeRegistry) UnregisterNode(ctx context.Context, nodeID string) error {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	node, exists := nr.nodes[nodeID]
	if !exists {
		return fmt.Errorf("node %s not found", nodeID)
	}

	// 删除节点
	delete(nr.nodes, nodeID)

	// 发送事件
	nr.sendEvent(&NodeEvent{
		Type: "unregister",
		Node: node,
		Time: time.Now(),
	})

	nr.logger.Info(fmt.Sprintf("Node unregistered: %s", nodeID))
	return nil
}

// GetNode 获取节点信息
func (nr *NodeRegistry) GetNode(ctx context.Context, nodeID string) (*model.Node, error) {
	nr.mu.RLock()
	defer nr.mu.RUnlock()

	node, exists := nr.nodes[nodeID]
	if !exists {
		return nil, fmt.Errorf("node %s not found", nodeID)
	}

	return node, nil
}

// ListNodes 列出所有节点
func (nr *NodeRegistry) ListNodes(ctx context.Context) ([]*model.Node, error) {
	nr.mu.RLock()
	defer nr.mu.RUnlock()

	nodes := make([]*model.Node, 0, len(nr.nodes))
	for _, node := range nr.nodes {
		nodes = append(nodes, node)
	}

	return nodes, nil
}

// ListNodesByType 根据类型列出节点
func (nr *NodeRegistry) ListNodesByType(ctx context.Context, nodeType model.NodeType) ([]*model.Node, error) {
	nr.mu.RLock()
	defer nr.mu.RUnlock()

	var nodes []*model.Node
	for _, node := range nr.nodes {
		if node.Type == nodeType {
			nodes = append(nodes, node)
		}
	}

	return nodes, nil
}

// ListNodesByStatus 根据状态列出节点
func (nr *NodeRegistry) ListNodesByStatus(ctx context.Context, status model.NodeStatus) ([]*model.Node, error) {
	nr.mu.RLock()
	defer nr.mu.RUnlock()

	var nodes []*model.Node
	for _, node := range nr.nodes {
		if node.Status == status {
			nodes = append(nodes, node)
		}
	}

	return nodes, nil
}

// UpdateNodeStatus 更新节点状态
func (nr *NodeRegistry) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	node, exists := nr.nodes[nodeID]
	if !exists {
		return fmt.Errorf("node %s not found", nodeID)
	}

	oldStatus := node.Status
	node.Status = status
	node.UpdatedTime = time.Now()

	// 如果状态发生变化，发送事件
	if oldStatus != status {
		nr.sendEvent(&NodeEvent{
			Type: "update",
			Node: node,
			Time: time.Now(),
		})
	}

	nr.logger.Debug(fmt.Sprintf("Node %s status updated: %s -> %s", nodeID, oldStatus, status))
	return nil
}

// Heartbeat 节点心跳
func (nr *NodeRegistry) Heartbeat(ctx context.Context, nodeID string) error {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	node, exists := nr.nodes[nodeID]
	if !exists {
		return fmt.Errorf("node %s not found", nodeID)
	}

	node.LastHeartbeat = time.Now()
	node.UpdatedTime = time.Now()

	// 如果节点之前是离线状态，更新为在线
	if node.Status == model.NodeStatusOffline {
		node.Status = model.NodeStatusOnline
		nr.sendEvent(&NodeEvent{
			Type: "update",
			Node: node,
			Time: time.Now(),
		})
	}

	nr.logger.Debug(fmt.Sprintf("Heartbeat received from node: %s", nodeID))
	return nil
}

// WatchNodes 监听节点变化
func (nr *NodeRegistry) WatchNodes(ctx context.Context) (<-chan *NodeEvent, error) {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	watcher := make(chan *NodeEvent, 100)
	nr.watchers = append(nr.watchers, watcher)

	nr.logger.Debug("Node watcher added")
	return watcher, nil
}

// sendEvent 发送事件到所有监听器
func (nr *NodeRegistry) sendEvent(event *NodeEvent) {
	for _, watcher := range nr.watchers {
		select {
		case watcher <- event:
		default:
			// 监听器通道已满，跳过
			nr.logger.Warn("Node watcher channel full, dropping event")
		}
	}
}

// healthCheckLoop 健康检查循环
func (nr *NodeRegistry) healthCheckLoop() {
	defer nr.wg.Done()

	ticker := time.NewTicker(60 * time.Second) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			nr.checkNodeHealth()
		case <-nr.ctx.Done():
			return
		}
	}
}

// checkNodeHealth 检查节点健康状态
func (nr *NodeRegistry) checkNodeHealth() {
	nr.mu.Lock()
	defer nr.mu.Unlock()

	now := time.Now()
	timeout := 2 * time.Minute // 2分钟超时

	for nodeID, node := range nr.nodes {
		if node.Status == model.NodeStatusOnline {
			// 检查心跳超时
			if now.Sub(node.LastHeartbeat) > timeout {
				nr.logger.Warn(fmt.Sprintf("Node %s heartbeat timeout, marking as offline", nodeID))
				node.Status = model.NodeStatusOffline
				node.UpdatedTime = now

				nr.sendEvent(&NodeEvent{
					Type: "update",
					Node: node,
					Time: now,
				})
			}
		}
	}
}
