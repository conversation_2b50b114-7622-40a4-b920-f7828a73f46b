package repository

import (
	"context"
	"golem-backend/internal/model"
)

// UserRepository 用户仓库接口
type UserRepository interface {
	// GetUserByID 根据 ID 获取用户
	GetUserByID(ctx context.Context, id string) (*model.User, error)

	// GetUserByUsername 根据用户名获取用户
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, user *model.User) error

	// UpdateUser 更新用户
	UpdateUser(ctx context.Context, user *model.User) error

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, id string) error
}

// userRepository 用户仓库实现
type userRepository struct {
}

// NewUserRepository 创建用户仓库
func NewUserRepository() UserRepository {
	return &userRepository{}
}
func (u *userRepository) GetUserByID(ctx context.Context, id string) (*model.User, error) {
	// TODO implement me
	panic("implement me")
}
func (u *userRepository) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	// TODO implement me
	panic("implement me")
}

func (u *userRepository) CreateUser(ctx context.Context, user *model.User) error {
	// TODO implement me
	panic("implement me")
}

func (u *userRepository) UpdateUser(ctx context.Context, user *model.User) error {
	// TODO implement me
	panic("implement me")
}

func (u *userRepository) DeleteUser(ctx context.Context, id string) error {
	// TODO implement me
	panic("implement me")
}
