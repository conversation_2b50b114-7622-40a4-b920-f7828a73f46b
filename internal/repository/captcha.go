package repository

import (
	"context"
	"github.com/redis/go-redis/v9"
	"time"
)

// CaptchaRepository 验证码仓库接口
type CaptchaRepository interface {
	// SaveCaptcha 保存验证码
	SaveCaptcha(ctx context.Context, key, value string, expire time.Duration) error

	// GetCaptcha 获取验证码
	GetCaptcha(ctx context.Context, key string) (string, error)

	// DeleteCaptcha 删除验证码
	DeleteCaptcha(ctx context.Context, key string) error
}

// captchaRepository 验证码仓库实现
type captchaRepository struct {
	rdb *redis.Client
}

// NewCaptchaRepository 创建验证码仓库
func NewCaptchaRepository(rdb *redis.Client) CaptchaRepository {
	return &captchaRepository{rdb}
}

// SaveCaptcha 保存验证码
func (r *captchaRepository) SaveCaptcha(ctx context.Context, key, value string, expire time.Duration) error {
	return r.rdb.Set(ctx, key, value, expire).Err()
}

// GetCaptcha 获取验证码
func (r *captchaRepository) GetCaptcha(ctx context.Context, key string) (string, error) {
	return r.rdb.Get(ctx, key).Result()
}

// DeleteCaptcha 删除验证码
func (r *captchaRepository) DeleteCaptcha(ctx context.Context, key string) error {
	return r.rdb.Del(ctx, key).Err()
}
