package repository

import (
	"context"
	"golem-backend/internal/model"
)

// MenuRepository 菜单仓库接口
type MenuRepository interface {
	// GetMenuByID 根据菜单 ID 获取菜单
	GetMenuByID(ctx context.Context, id string) (*model.Menu, error)

	// CreateMenu 创建菜单
	CreateMenu(ctx context.Context, menu *model.Menu) error

	// UpdateMenu 更新菜单
	UpdateMenu(ctx context.Context, menu *model.Menu) error

	// DeleteMenu 删除菜单
	DeleteMenu(ctx context.Context, id string) error
}

// menuRepository 菜单仓库实现
type menuRepository struct {
}

// NewMenuRepository 创建菜单仓库
func NewMenuRepository() MenuRepository {
	return &menuRepository{}
}

// GetMenuByID 根据菜单 ID 获取菜单
func (r *menuRepository) GetMenuByID(ctx context.Context, id string) (*model.Menu, error) {
	// TODO implement me
	panic("implement me")
}

// CreateMenu 创建菜单
func (r *menuRepository) CreateMenu(ctx context.Context, menu *model.Menu) error {
	// TODO implement me
	panic("implement me")
}

// UpdateMenu 更新菜单
func (r *menuRepository) UpdateMenu(ctx context.Context, menu *model.Menu) error {
	// TODO implement me
	panic("implement me")
}

// DeleteMenu 删除菜单
func (r *menuRepository) DeleteMenu(ctx context.Context, id string) error {
	// TODO implement me
	panic("implement me")
}
