package repository

import (
	"context"
	"golem-backend/internal/model"
)

// MappingRepository 资产测绘仓库接口
type MappingRepository interface {
	// SearchMappings 搜索
	SearchMappings(ctx context.Context, query string, offset, limit int) ([]*model.Mapping, int64, error)
}

// mappingRepository 资产测绘仓库实现
type mappingRepository struct {
}

// NewMappingRepository 创建资产测绘仓库
func NewMappingRepository() MappingRepository {
	return &mappingRepository{}
}

// SearchMappings 搜索
func (r *mappingRepository) SearchMappings(ctx context.Context, query string, offset, limit int) ([]*model.Mapping, int64, error) {
	// TODO implement me
	panic("implement me")
}
