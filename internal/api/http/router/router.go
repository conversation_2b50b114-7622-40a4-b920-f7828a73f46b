package router

import (
	"github.com/gin-gonic/gin"
	"golem-backend/internal/api/http/middleware"
	"golem-backend/internal/consts"
)

// SetupManageRoutes 设置管理平台路由
func SetupManageRoutes(e *gin.Engine) *gin.RouterGroup {
	// 注册 CORS 跨域中间件
	e.Use(middleware.CORS())
	// 注册日志中间件
	e.Use(middleware.Logging())
	// 注册恢复中间件
	e.Use(middleware.Recovery())
	// 注册请求中间件
	e.Use(middleware.Request())
	// 注册限流中间件
	e.Use(middleware.Limiter())
	// 注册超时中间件
	e.Use(middleware.Timeout(consts.Timeout))

	// API根路由
	r := e.Group(consts.ApiRootGroup)

	return r
}
