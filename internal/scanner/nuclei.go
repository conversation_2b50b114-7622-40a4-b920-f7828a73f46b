package scanner

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// NucleiScanner Nuclei扫描器实现
type NucleiScanner struct {
	*BaseScanner
	nucleiPath     string
	templatesPath  string
	maxConcurrency int
	timeout        time.Duration
	rateLimit      int
}

// NucleiConfig Nuclei配置
type NucleiConfig struct {
	NucleiPath     string        `json:"nuclei_path"`     // Nuclei可执行文件路径
	TemplatesPath  string        `json:"templates_path"`  // 模板路径
	MaxConcurrency int           `json:"max_concurrency"` // 最大并发数
	Timeout        time.Duration `json:"timeout"`         // 扫描超时时间
	RateLimit      int           `json:"rate_limit"`      // 速率限制 (requests/sec)
	Severity       []string      `json:"severity"`        // 严重性过滤
	Tags           []string      `json:"tags"`            // 标签过滤
	Templates      []string      `json:"templates"`       // 指定模板
	ExcludeTags    []string      `json:"exclude_tags"`    // 排除标签
}

// NucleiResult Nuclei JSON结果结构
type NucleiResult struct {
	TemplateID   string                 `json:"template-id"`
	TemplatePath string                 `json:"template-path"`
	Info         NucleiTemplateInfo     `json:"info"`
	Type         string                 `json:"type"`
	Host         string                 `json:"host"`
	MatchedAt    string                 `json:"matched-at"`
	ExtractedResults []string           `json:"extracted-results,omitempty"`
	Request      string                 `json:"request,omitempty"`
	Response     string                 `json:"response,omitempty"`
	IP           string                 `json:"ip,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
	CURLCommand  string                 `json:"curl-command,omitempty"`
	Matcher      []NucleiMatcher        `json:"matcher-status,omitempty"`
	Meta         map[string]interface{} `json:"meta,omitempty"`
}

// NucleiTemplateInfo 模板信息
type NucleiTemplateInfo struct {
	Name           string            `json:"name"`
	Author         []string          `json:"author"`
	Tags           []string          `json:"tags"`
	Description    string            `json:"description"`
	Reference      []string          `json:"reference,omitempty"`
	Severity       string            `json:"severity"`
	Confidence     string            `json:"confidence,omitempty"`
	Classification NucleiClassification `json:"classification,omitempty"`
	Remediation    string            `json:"remediation,omitempty"`
}

// NucleiClassification 漏洞分类
type NucleiClassification struct {
	CVEID       []string `json:"cve-id,omitempty"`
	CWEID       []string `json:"cwe-id,omitempty"`
	CVSS        string   `json:"cvss-metrics,omitempty"`
	CVSSScore   float64  `json:"cvss-score,omitempty"`
	EPSS        float64  `json:"epss-score,omitempty"`
	CPE         string   `json:"cpe,omitempty"`
}

// NucleiMatcher 匹配器状态
type NucleiMatcher struct {
	Name    string `json:"name"`
	Status  bool   `json:"status"`
	Message string `json:"message,omitempty"`
}

// NucleiScanResult 扫描结果汇总
type NucleiScanResult struct {
	Vulnerabilities []NucleiVulnerability      `json:"vulnerabilities"`
	TotalFindings   int                        `json:"total_findings"`
	ScanDuration    float64                    `json:"scan_duration"`
	TemplatesUsed   int                        `json:"templates_used"`
	Statistics      map[string]interface{}     `json:"statistics"`
	SeverityStats   map[string]int             `json:"severity_stats"`
}

// NucleiVulnerability 漏洞信息
type NucleiVulnerability struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Severity     string                 `json:"severity"`
	Tags         []string               `json:"tags"`
	Reference    []string               `json:"reference,omitempty"`
	CVE          []string               `json:"cve,omitempty"`
	CWE          []string               `json:"cwe,omitempty"`
	CVSS         string                 `json:"cvss,omitempty"`
	CVSSScore    float64                `json:"cvss_score,omitempty"`
	Host         string                 `json:"host"`
	URL          string                 `json:"url"`
	Method       string                 `json:"method,omitempty"`
	Evidence     string                 `json:"evidence,omitempty"`
	Request      string                 `json:"request,omitempty"`
	Response     string                 `json:"response,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
	Remediation  string                 `json:"remediation,omitempty"`
	Meta         map[string]interface{} `json:"meta,omitempty"`
}

// NewNucleiScanner 创建Nuclei扫描器
func NewNucleiScanner(log logger.Logger, config *NucleiConfig) *NucleiScanner {
	if config == nil {
		config = &NucleiConfig{
			NucleiPath:     "nuclei",
			TemplatesPath:  "",
			MaxConcurrency: 25,
			Timeout:        10 * time.Minute,
			RateLimit:      150,
		}
	}

	capabilities := []string{string(model.TaskTypeVulnScan)}
	
	return &NucleiScanner{
		BaseScanner:    NewBaseScanner("nuclei", "3.1.0", capabilities, log),
		nucleiPath:     config.NucleiPath,
		templatesPath:  config.TemplatesPath,
		maxConcurrency: config.MaxConcurrency,
		timeout:        config.Timeout,
		rateLimit:      config.RateLimit,
	}
}

// Scan 执行Nuclei扫描
func (n *NucleiScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	n.logger.Info(fmt.Sprintf("Nuclei scanner executing task: %s", task.ID))
	
	// 验证任务类型
	if !n.IsSupported(task.Type) {
		return nil, fmt.Errorf("unsupported task type: %s", task.Type)
	}
	
	// 解析任务配置
	config, err := n.parseTaskConfig(task.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse task config: %w", err)
	}
	
	// 更新任务状态
	task.Status = model.TaskStatusRunning
	startTime := time.Now()
	task.StartTime = &startTime
	task.Progress = 0
	
	// 执行扫描
	result, err := n.executeScan(ctx, config, task)
	if err != nil {
		task.Status = model.TaskStatusFailed
		task.Error = err.Error()
		return task, err
	}
	
	// 设置扫描结果
	task.Result = map[string]interface{}{
		"scanner":     n.GetName(),
		"version":     n.GetVersion(),
		"scan_result": result,
		"scan_time":   time.Since(startTime).Seconds(),
	}
	
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	completedTime := time.Now()
	task.CompletedTime = &completedTime
	
	n.logger.Info(fmt.Sprintf("Nuclei scan completed for task: %s", task.ID))
	return task, nil
}

// parseTaskConfig 解析任务配置
func (n *NucleiScanner) parseTaskConfig(config map[string]interface{}) (*NucleiConfig, error) {
	nucleiConfig := &NucleiConfig{
		NucleiPath:     n.nucleiPath,
		TemplatesPath:  n.templatesPath,
		MaxConcurrency: n.maxConcurrency,
		Timeout:        n.timeout,
		RateLimit:      n.rateLimit,
	}
	
	// 从任务配置中覆盖默认值
	if templatesPath, ok := config["templates_path"].(string); ok {
		nucleiConfig.TemplatesPath = templatesPath
	}
	
	if concurrency, ok := config["max_concurrency"].(float64); ok {
		nucleiConfig.MaxConcurrency = int(concurrency)
	}
	
	if rateLimit, ok := config["rate_limit"].(float64); ok {
		nucleiConfig.RateLimit = int(rateLimit)
	}
	
	if timeout, ok := config["timeout"].(float64); ok {
		nucleiConfig.Timeout = time.Duration(timeout) * time.Second
	}
	
	// 解析严重性过滤
	if severity, ok := config["severity"].([]interface{}); ok {
		nucleiConfig.Severity = make([]string, len(severity))
		for i, s := range severity {
			if str, ok := s.(string); ok {
				nucleiConfig.Severity[i] = str
			}
		}
	}
	
	// 解析标签过滤
	if tags, ok := config["tags"].([]interface{}); ok {
		nucleiConfig.Tags = make([]string, len(tags))
		for i, tag := range tags {
			if str, ok := tag.(string); ok {
				nucleiConfig.Tags[i] = str
			}
		}
	}
	
	// 解析模板列表
	if templates, ok := config["templates"].([]interface{}); ok {
		nucleiConfig.Templates = make([]string, len(templates))
		for i, template := range templates {
			if str, ok := template.(string); ok {
				nucleiConfig.Templates[i] = str
			}
		}
	}
	
	// 解析排除标签
	if excludeTags, ok := config["exclude_tags"].([]interface{}); ok {
		nucleiConfig.ExcludeTags = make([]string, len(excludeTags))
		for i, tag := range excludeTags {
			if str, ok := tag.(string); ok {
				nucleiConfig.ExcludeTags[i] = str
			}
		}
	}
	
	return nucleiConfig, nil
}

// executeScan 执行扫描
func (n *NucleiScanner) executeScan(ctx context.Context, config *NucleiConfig, task *model.Task) (*NucleiScanResult, error) {
	// 获取目标
	target := getStringFromConfig(task.Config, "target", "")
	if target == "" {
		return nil, fmt.Errorf("target is required")
	}
	
	// 构建Nuclei命令
	args := n.buildNucleiArgs(target, config)
	
	// 创建带超时的上下文
	scanCtx, cancel := context.WithTimeout(ctx, config.Timeout)
	defer cancel()
	
	// 执行命令
	cmd := exec.CommandContext(scanCtx, n.nucleiPath, args...)
	
	n.logger.Debug(fmt.Sprintf("Executing nuclei command: %s %s", n.nucleiPath, strings.Join(args, " ")))
	
	// 更新进度
	task.Progress = 25
	
	// 获取命令输出
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}
	
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start nuclei: %w", err)
	}
	
	// 实时解析输出
	var results []NucleiResult
	scanner := bufio.NewScanner(stdout)
	
	for scanner.Scan() {
		select {
		case <-scanCtx.Done():
			cmd.Process.Kill()
			return nil, scanCtx.Err()
		default:
			line := scanner.Text()
			if line == "" {
				continue
			}
			
			// 解析JSON结果
			var result NucleiResult
			if err := json.Unmarshal([]byte(line), &result); err != nil {
				n.logger.Debug(fmt.Sprintf("Failed to parse nuclei output line: %s", line))
				continue
			}
			
			results = append(results, result)
			
			// 更新进度
			if len(results)%10 == 0 {
				task.Progress = 25 + (len(results)*50)/100 // 假设最多100个结果
			}
		}
	}
	
	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		n.logger.Debug(fmt.Sprintf("Nuclei command finished with error: %v", err))
	}
	
	// 更新进度
	task.Progress = 90
	
	// 转换为标准结果格式
	scanResult := n.convertToScanResult(results)

	return scanResult, nil
}

// buildNucleiArgs 构建Nuclei命令参数
func (n *NucleiScanner) buildNucleiArgs(target string, config *NucleiConfig) []string {
	args := []string{
		"-u", target,
		"-json",                                    // JSON输出格式
		"-c", strconv.Itoa(config.MaxConcurrency), // 并发数
		"-rl", strconv.Itoa(config.RateLimit),     // 速率限制
		"-silent",                                  // 静默模式
		"-no-color",                               // 禁用颜色输出
	}

	// 添加模板路径
	if config.TemplatesPath != "" {
		args = append(args, "-t", config.TemplatesPath)
	}

	// 添加指定模板
	if len(config.Templates) > 0 {
		for _, template := range config.Templates {
			args = append(args, "-t", template)
		}
	}

	// 添加严重性过滤
	if len(config.Severity) > 0 {
		args = append(args, "-severity", strings.Join(config.Severity, ","))
	}

	// 添加标签过滤
	if len(config.Tags) > 0 {
		args = append(args, "-tags", strings.Join(config.Tags, ","))
	}

	// 添加排除标签
	if len(config.ExcludeTags) > 0 {
		args = append(args, "-exclude-tags", strings.Join(config.ExcludeTags, ","))
	}

	// 添加其他选项
	args = append(args, "-timeout", "30") // 单个请求超时
	args = append(args, "-retries", "1")  // 重试次数

	return args
}

// convertToScanResult 转换为标准扫描结果格式
func (n *NucleiScanner) convertToScanResult(results []NucleiResult) *NucleiScanResult {
	scanResult := &NucleiScanResult{
		Vulnerabilities: make([]NucleiVulnerability, 0),
		TotalFindings:   len(results),
		Statistics:      make(map[string]interface{}),
		SeverityStats:   make(map[string]int),
	}

	// 转换每个结果
	for _, result := range results {
		vuln := n.convertToVulnerability(result)
		scanResult.Vulnerabilities = append(scanResult.Vulnerabilities, vuln)

		// 统计严重性
		severity := strings.ToLower(vuln.Severity)
		scanResult.SeverityStats[severity]++
	}

	// 添加统计信息
	scanResult.Statistics["total_templates"] = len(results)
	scanResult.Statistics["unique_vulnerabilities"] = n.countUniqueVulnerabilities(scanResult.Vulnerabilities)
	scanResult.Statistics["affected_hosts"] = n.countAffectedHosts(scanResult.Vulnerabilities)

	return scanResult
}

// convertToVulnerability 转换为漏洞信息
func (n *NucleiScanner) convertToVulnerability(result NucleiResult) NucleiVulnerability {
	vuln := NucleiVulnerability{
		ID:          result.TemplateID,
		Name:        result.Info.Name,
		Description: result.Info.Description,
		Severity:    result.Info.Severity,
		Tags:        result.Info.Tags,
		Reference:   result.Info.Reference,
		Host:        result.Host,
		URL:         result.MatchedAt,
		Request:     result.Request,
		Response:    result.Response,
		Timestamp:   result.Timestamp,
		Remediation: result.Info.Remediation,
		Meta:        result.Meta,
	}

	// 提取CVE信息
	if len(result.Info.Classification.CVEID) > 0 {
		vuln.CVE = result.Info.Classification.CVEID
	}

	// 提取CWE信息
	if len(result.Info.Classification.CWEID) > 0 {
		vuln.CWE = result.Info.Classification.CWEID
	}

	// 提取CVSS信息
	if result.Info.Classification.CVSS != "" {
		vuln.CVSS = result.Info.Classification.CVSS
		vuln.CVSSScore = result.Info.Classification.CVSSScore
	}

	// 提取证据信息
	if len(result.ExtractedResults) > 0 {
		vuln.Evidence = strings.Join(result.ExtractedResults, "; ")
	}

	// 确定HTTP方法
	if strings.Contains(result.Request, "GET ") {
		vuln.Method = "GET"
	} else if strings.Contains(result.Request, "POST ") {
		vuln.Method = "POST"
	} else if strings.Contains(result.Request, "PUT ") {
		vuln.Method = "PUT"
	} else if strings.Contains(result.Request, "DELETE ") {
		vuln.Method = "DELETE"
	}

	return vuln
}

// countUniqueVulnerabilities 统计唯一漏洞数量
func (n *NucleiScanner) countUniqueVulnerabilities(vulnerabilities []NucleiVulnerability) int {
	unique := make(map[string]bool)
	for _, vuln := range vulnerabilities {
		unique[vuln.ID] = true
	}
	return len(unique)
}

// countAffectedHosts 统计受影响主机数量
func (n *NucleiScanner) countAffectedHosts(vulnerabilities []NucleiVulnerability) int {
	hosts := make(map[string]bool)
	for _, vuln := range vulnerabilities {
		hosts[vuln.Host] = true
	}
	return len(hosts)
}

// GetSeverityLevel 获取严重性级别数值
func (n *NucleiScanner) GetSeverityLevel(severity string) int {
	switch strings.ToLower(severity) {
	case "critical":
		return 4
	case "high":
		return 3
	case "medium":
		return 2
	case "low":
		return 1
	case "info":
		return 0
	default:
		return -1
	}
}

// FilterBySeverity 按严重性过滤漏洞
func (n *NucleiScanner) FilterBySeverity(vulnerabilities []NucleiVulnerability, minSeverity string) []NucleiVulnerability {
	minLevel := n.GetSeverityLevel(minSeverity)
	if minLevel < 0 {
		return vulnerabilities
	}

	var filtered []NucleiVulnerability
	for _, vuln := range vulnerabilities {
		if n.GetSeverityLevel(vuln.Severity) >= minLevel {
			filtered = append(filtered, vuln)
		}
	}

	return filtered
}

// GenerateReport 生成扫描报告
func (n *NucleiScanner) GenerateReport(result *NucleiScanResult) map[string]interface{} {
	report := make(map[string]interface{})

	// 基本统计
	report["summary"] = map[string]interface{}{
		"total_findings":         result.TotalFindings,
		"unique_vulnerabilities": result.Statistics["unique_vulnerabilities"],
		"affected_hosts":         result.Statistics["affected_hosts"],
		"scan_duration":          result.ScanDuration,
	}

	// 严重性分布
	report["severity_distribution"] = result.SeverityStats

	// 按严重性分组的漏洞
	severityGroups := make(map[string][]NucleiVulnerability)
	for _, vuln := range result.Vulnerabilities {
		severity := strings.ToLower(vuln.Severity)
		severityGroups[severity] = append(severityGroups[severity], vuln)
	}
	report["vulnerabilities_by_severity"] = severityGroups

	// Top 10 漏洞类型
	vulnTypes := make(map[string]int)
	for _, vuln := range result.Vulnerabilities {
		vulnTypes[vuln.ID]++
	}
	report["top_vulnerability_types"] = n.getTopVulnerabilityTypes(vulnTypes, 10)

	// 受影响的主机列表
	affectedHosts := make(map[string][]string)
	for _, vuln := range result.Vulnerabilities {
		affectedHosts[vuln.Host] = append(affectedHosts[vuln.Host], vuln.ID)
	}
	report["affected_hosts"] = affectedHosts

	return report
}

// getTopVulnerabilityTypes 获取Top N漏洞类型
func (n *NucleiScanner) getTopVulnerabilityTypes(vulnTypes map[string]int, topN int) []map[string]interface{} {
	type vulnCount struct {
		Type  string
		Count int
	}

	var counts []vulnCount
	for vulnType, count := range vulnTypes {
		counts = append(counts, vulnCount{Type: vulnType, Count: count})
	}

	// 简单排序（冒泡排序）
	for i := 0; i < len(counts)-1; i++ {
		for j := 0; j < len(counts)-i-1; j++ {
			if counts[j].Count < counts[j+1].Count {
				counts[j], counts[j+1] = counts[j+1], counts[j]
			}
		}
	}

	// 取前N个
	if len(counts) > topN {
		counts = counts[:topN]
	}

	var result []map[string]interface{}
	for _, count := range counts {
		result = append(result, map[string]interface{}{
			"type":  count.Type,
			"count": count.Count,
		})
	}

	return result
}
