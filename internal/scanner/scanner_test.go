package scanner

import (
	"testing"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// mockLogger 模拟日志器
type mockLogger struct{}

func (m *mockLogger) Trace(args ...interface{})   {}
func (m *mockLogger) Debug(args ...interface{})   {}
func (m *mockLogger) Print(args ...interface{})   {}
func (m *mockLogger) Info(args ...interface{})    {}
func (m *mockLogger) Warn(args ...interface{})    {}
func (m *mockLogger) Warning(args ...interface{}) {}
func (m *mockLogger) Error(args ...interface{})   {}
func (m *mockLogger) Panic(args ...interface{})   {}
func (m *mockLogger) Fatal(args ...interface{})   {}

func newMockLogger() logger.Logger {
	return &mockLogger{}
}

// TestScannerFactory 测试扫描器工厂
func TestScannerFactory(t *testing.T) {
	log := newMockLogger()
	factory := NewScannerFactory(log)

	// 测试获取扫描器
	scanners := factory.ListScanners()
	if len(scanners) == 0 {
		t.Error("Expected at least one scanner to be registered")
	}

	// 测试获取ZMap扫描器
	zmapScanner, err := factory.GetScanner("zmap")
	if err != nil {
		t.Errorf("Expected to find zmap scanner, got error: %v", err)
	}

	if zmapScanner.GetName() != "zmap" {
		t.Errorf("Expected scanner name 'zmap', got '%s'", zmapScanner.GetName())
	}

	// 测试获取Nmap扫描器
	nmapScanner, err := factory.GetScanner("nmap")
	if err != nil {
		t.Errorf("Expected to find nmap scanner, got error: %v", err)
	}

	if nmapScanner.GetName() != "nmap" {
		t.Errorf("Expected scanner name 'nmap', got '%s'", nmapScanner.GetName())
	}

	// 测试获取Nuclei扫描器
	nucleiScanner, err := factory.GetScanner("nuclei")
	if err != nil {
		t.Errorf("Expected to find nuclei scanner, got error: %v", err)
	}

	if nucleiScanner.GetName() != "nuclei" {
		t.Errorf("Expected scanner name 'nuclei', got '%s'", nucleiScanner.GetName())
	}
}

// TestScannerByTaskType 测试根据任务类型获取扫描器
func TestScannerByTaskType(t *testing.T) {
	log := newMockLogger()
	factory := NewScannerFactory(log)

	testCases := []struct {
		taskType        model.TaskType
		expectedScanner string
	}{
		{model.TaskTypePortScan, "zmap"},
		{model.TaskTypeServiceDetect, "nmap"},
		{model.TaskTypeFingerprintScan, "nmap"},
		{model.TaskTypeVulnScan, "nuclei"},
	}

	for _, tc := range testCases {
		scanner, err := factory.GetScannerByTaskType(tc.taskType)
		if err != nil {
			t.Errorf("Expected to find scanner for task type %s, got error: %v", tc.taskType, err)
			continue
		}

		if scanner.GetName() != tc.expectedScanner {
			t.Errorf("Expected scanner '%s' for task type %s, got '%s'",
				tc.expectedScanner, tc.taskType, scanner.GetName())
		}
	}
}

// TestZMapScanner 测试ZMap扫描器
func TestZMapScanner(t *testing.T) {
	log := newMockLogger()
	scanner := NewZMapScanner(log, nil)

	// 测试基本属性
	if scanner.GetName() != "zmap" {
		t.Errorf("Expected scanner name 'zmap', got '%s'", scanner.GetName())
	}

	// 测试支持的任务类型
	if !scanner.IsSupported(model.TaskTypePortScan) {
		t.Error("Expected ZMap scanner to support port scan")
	}

	if scanner.IsSupported(model.TaskTypeVulnScan) {
		t.Error("Expected ZMap scanner not to support vulnerability scan")
	}

	// 测试能力列表
	capabilities := scanner.GetCapabilities()
	if len(capabilities) == 0 {
		t.Error("Expected ZMap scanner to have capabilities")
	}

	found := false
	for _, cap := range capabilities {
		if cap == string(model.TaskTypePortScan) {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected ZMap scanner to have port_scan capability")
	}
}

// TestNmapScanner 测试Nmap扫描器
func TestNmapScanner(t *testing.T) {
	log := newMockLogger()
	scanner := NewNmapScanner(log, nil)

	// 测试基本属性
	if scanner.GetName() != "nmap" {
		t.Errorf("Expected scanner name 'nmap', got '%s'", scanner.GetName())
	}

	// 测试支持的任务类型
	supportedTypes := []model.TaskType{
		model.TaskTypePortScan,
		model.TaskTypeServiceDetect,
		model.TaskTypeFingerprintScan,
	}

	for _, taskType := range supportedTypes {
		if !scanner.IsSupported(taskType) {
			t.Errorf("Expected Nmap scanner to support %s", taskType)
		}
	}

	if scanner.IsSupported(model.TaskTypeVulnScan) {
		t.Error("Expected Nmap scanner not to support vulnerability scan")
	}
}

// TestNucleiScanner 测试Nuclei扫描器
func TestNucleiScanner(t *testing.T) {
	log := newMockLogger()
	scanner := NewNucleiScanner(log, nil)

	// 测试基本属性
	if scanner.GetName() != "nuclei" {
		t.Errorf("Expected scanner name 'nuclei', got '%s'", scanner.GetName())
	}

	// 测试支持的任务类型
	if !scanner.IsSupported(model.TaskTypeVulnScan) {
		t.Error("Expected Nuclei scanner to support vulnerability scan")
	}

	if scanner.IsSupported(model.TaskTypePortScan) {
		t.Error("Expected Nuclei scanner not to support port scan")
	}
}

// TestScannerConfiguration 测试扫描器配置
func TestScannerConfiguration(t *testing.T) {
	log := newMockLogger()

	// 测试ZMap配置
	zmapConfig := &ZMapConfig{
		ZMapPath:     "/usr/bin/zmap",
		MaxRate:      1000,
		Bandwidth:    "1M",
		CooldownTime: 5,
	}

	zmapScanner := NewZMapScanner(log, zmapConfig)
	if zmapScanner.zmapPath != "/usr/bin/zmap" {
		t.Errorf("Expected zmap path '/usr/bin/zmap', got '%s'", zmapScanner.zmapPath)
	}

	// 测试Nmap配置
	nmapConfig := &NmapConfig{
		NmapPath:    "/usr/bin/nmap",
		MaxParallel: 50,
		Timeout:     2 * time.Minute,
	}

	nmapScanner := NewNmapScanner(log, nmapConfig)
	if nmapScanner.nmapPath != "/usr/bin/nmap" {
		t.Errorf("Expected nmap path '/usr/bin/nmap', got '%s'", nmapScanner.nmapPath)
	}

	// 测试Nuclei配置
	nucleiConfig := &NucleiConfig{
		NucleiPath:     "/usr/bin/nuclei",
		TemplatesPath:  "/opt/nuclei-templates",
		MaxConcurrency: 10,
		RateLimit:      100,
	}

	nucleiScanner := NewNucleiScanner(log, nucleiConfig)
	if nucleiScanner.nucleiPath != "/usr/bin/nuclei" {
		t.Errorf("Expected nuclei path '/usr/bin/nuclei', got '%s'", nucleiScanner.nucleiPath)
	}
}

// TestConfigurableFactory 测试可配置工厂
func TestConfigurableFactory(t *testing.T) {
	log := newMockLogger()

	config := &ScannerConfig{
		ZMap: &ZMapConfig{
			ZMapPath: "/custom/zmap",
			MaxRate:  5000,
		},
		Nmap: &NmapConfig{
			NmapPath:    "/custom/nmap",
			MaxParallel: 200,
		},
		Nuclei: &NucleiConfig{
			NucleiPath:     "/custom/nuclei",
			MaxConcurrency: 50,
		},
	}

	factory := NewConfigurableFactory(log, config)

	// 测试获取配置
	currentConfig := factory.GetConfig()
	if currentConfig.ZMap.ZMapPath != "/custom/zmap" {
		t.Errorf("Expected custom zmap path, got '%s'", currentConfig.ZMap.ZMapPath)
	}

	// 测试更新配置
	newConfig := &ScannerConfig{
		ZMap: &ZMapConfig{
			ZMapPath: "/new/zmap",
			MaxRate:  10000,
		},
	}

	err := factory.UpdateConfig(newConfig)
	if err != nil {
		t.Errorf("Expected no error updating config, got: %v", err)
	}

	updatedConfig := factory.GetConfig()
	if updatedConfig.ZMap.ZMapPath != "/new/zmap" {
		t.Errorf("Expected updated zmap path, got '%s'", updatedConfig.ZMap.ZMapPath)
	}
}

// TestScannerStats 测试扫描器统计
func TestScannerStats(t *testing.T) {
	log := newMockLogger()
	factory := NewScannerFactory(log)

	stats := factory.GetScannerStats()

	totalScanners, ok := stats["total_scanners"].(int)
	if !ok || totalScanners == 0 {
		t.Error("Expected total_scanners to be greater than 0")
	}

	capabilityDist, ok := stats["capability_distribution"].(map[string]int)
	if !ok {
		t.Error("Expected capability_distribution to be present")
	}

	// 检查是否有端口扫描能力
	if portScanCount, exists := capabilityDist[string(model.TaskTypePortScan)]; !exists || portScanCount == 0 {
		t.Error("Expected at least one scanner with port scan capability")
	}
}

// TestPortStringParsing 测试端口字符串解析
func TestPortStringParsing(t *testing.T) {
	testCases := []struct {
		input    string
		expected []int
	}{
		{"80", []int{80}},
		{"80,443", []int{80, 443}},
		{"80-85", []int{80, 81, 82, 83, 84, 85}},
		{"80,443,8080-8082", []int{80, 443, 8080, 8081, 8082}},
		{"", []int{}},
	}

	for _, tc := range testCases {
		result := parsePortString(tc.input)

		if len(result) != len(tc.expected) {
			t.Errorf("For input '%s', expected %d ports, got %d", tc.input, len(tc.expected), len(result))
			continue
		}

		for i, port := range result {
			if port != tc.expected[i] {
				t.Errorf("For input '%s', expected port %d at index %d, got %d", tc.input, tc.expected[i], i, port)
			}
		}
	}
}

// BenchmarkScannerFactory 基准测试扫描器工厂
func BenchmarkScannerFactory(b *testing.B) {
	log := newMockLogger()
	factory := NewScannerFactory(log)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := factory.GetScannerByTaskType(model.TaskTypePortScan)
		if err != nil {
			b.Errorf("Unexpected error: %v", err)
		}
	}
}
