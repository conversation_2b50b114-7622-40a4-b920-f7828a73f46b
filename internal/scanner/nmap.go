package scanner

import (
	"context"
	"encoding/xml"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// NmapScanner Nmap扫描器实现
type NmapScanner struct {
	*BaseScanner
	nmapPath    string
	maxParallel int
	timeout     time.Duration
}

// NmapConfig Nmap配置
type NmapConfig struct {
	NmapPath    string        `json:"nmap_path"`    // Nmap可执行文件路径
	MaxParallel int           `json:"max_parallel"` // 最大并行扫描数
	Timeout     time.Duration `json:"timeout"`      // 扫描超时时间
	ScanType    string        `json:"scan_type"`    // 扫描类型 (syn, tcp, udp, etc.)
	Timing      string        `json:"timing"`       // 时序模板 (T0-T5)
	Scripts     []string      `json:"scripts"`      // NSE脚本列表
}

// NmapResult Nmap XML结果结构
type NmapResult struct {
	XMLName xml.Name `xml:"nmaprun"`
	Scanner string   `xml:"scanner,attr"`
	Args    string   `xml:"args,attr"`
	Start   string   `xml:"start,attr"`
	Version string   `xml:"version,attr"`
	
	ScanInfo ScanInfo `xml:"scaninfo"`
	Verbose  Verbose  `xml:"verbose"`
	Debug    Debug    `xml:"debugging"`
	
	Hosts   []Host    `xml:"host"`
	RunStats RunStats `xml:"runstats"`
}

// ScanInfo 扫描信息
type ScanInfo struct {
	Type        string `xml:"type,attr"`
	Protocol    string `xml:"protocol,attr"`
	NumServices int    `xml:"numservices,attr"`
	Services    string `xml:"services,attr"`
}

// Verbose 详细信息
type Verbose struct {
	Level int `xml:"level,attr"`
}

// Debug 调试信息
type Debug struct {
	Level int `xml:"level,attr"`
}

// Host 主机信息
type Host struct {
	StartTime string `xml:"starttime,attr"`
	EndTime   string `xml:"endtime,attr"`
	
	Status    Status     `xml:"status"`
	Address   []Address  `xml:"address"`
	Hostnames Hostnames  `xml:"hostnames"`
	Ports     Ports      `xml:"ports"`
	OS        OS         `xml:"os"`
	Scripts   []Script   `xml:"script"`
	Times     Times      `xml:"times"`
}

// Status 主机状态
type Status struct {
	State     string `xml:"state,attr"`
	Reason    string `xml:"reason,attr"`
	ReasonTTL string `xml:"reason_ttl,attr"`
}

// Address 地址信息
type Address struct {
	Addr     string `xml:"addr,attr"`
	AddrType string `xml:"addrtype,attr"`
	Vendor   string `xml:"vendor,attr"`
}

// Hostnames 主机名
type Hostnames struct {
	Hostname []Hostname `xml:"hostname"`
}

// Hostname 主机名
type Hostname struct {
	Name string `xml:"name,attr"`
	Type string `xml:"type,attr"`
}

// Ports 端口信息
type Ports struct {
	ExtraPorts []ExtraPort `xml:"extraports"`
	Port       []Port      `xml:"port"`
}

// ExtraPort 额外端口信息
type ExtraPort struct {
	State string `xml:"state,attr"`
	Count int    `xml:"count,attr"`
}

// Port 端口详细信息
type Port struct {
	Protocol string `xml:"protocol,attr"`
	PortID   int    `xml:"portid,attr"`
	
	State   State     `xml:"state"`
	Service Service   `xml:"service"`
	Scripts []Script  `xml:"script"`
}

// State 端口状态
type State struct {
	State     string `xml:"state,attr"`
	Reason    string `xml:"reason,attr"`
	ReasonTTL string `xml:"reason_ttl,attr"`
}

// Service 服务信息
type Service struct {
	Name       string `xml:"name,attr"`
	Product    string `xml:"product,attr"`
	Version    string `xml:"version,attr"`
	ExtraInfo  string `xml:"extrainfo,attr"`
	Method     string `xml:"method,attr"`
	Conf       string `xml:"conf,attr"`
	CPE        []CPE  `xml:"cpe"`
}

// CPE 通用平台枚举
type CPE struct {
	Value string `xml:",chardata"`
}

// Script 脚本结果
type Script struct {
	ID     string `xml:"id,attr"`
	Output string `xml:"output,attr"`
	Table  []Table `xml:"table"`
}

// Table 脚本表格数据
type Table struct {
	Key   string  `xml:"key,attr"`
	Table []Table `xml:"table"`
	Elem  []Elem  `xml:"elem"`
}

// Elem 表格元素
type Elem struct {
	Key   string `xml:"key,attr"`
	Value string `xml:",chardata"`
}

// OS 操作系统信息
type OS struct {
	PortUsed    []PortUsed    `xml:"portused"`
	OSMatch     []OSMatch     `xml:"osmatch"`
	OSFingerprint OSFingerprint `xml:"osfingerprint"`
}

// PortUsed 用于OS检测的端口
type PortUsed struct {
	State    string `xml:"state,attr"`
	Proto    string `xml:"proto,attr"`
	PortID   int    `xml:"portid,attr"`
}

// OSMatch OS匹配结果
type OSMatch struct {
	Name     string    `xml:"name,attr"`
	Accuracy string    `xml:"accuracy,attr"`
	Line     string    `xml:"line,attr"`
	OSClass  []OSClass `xml:"osclass"`
}

// OSClass OS分类
type OSClass struct {
	Type     string `xml:"type,attr"`
	Vendor   string `xml:"vendor,attr"`
	OSFamily string `xml:"osfamily,attr"`
	OSGen    string `xml:"osgen,attr"`
	Accuracy string `xml:"accuracy,attr"`
}

// OSFingerprint OS指纹
type OSFingerprint struct {
	Fingerprint string `xml:"fingerprint,attr"`
}

// Times 时间信息
type Times struct {
	SRTT string `xml:"srtt,attr"`
	RTT  string `xml:"rttvar,attr"`
	To   string `xml:"to,attr"`
}

// RunStats 运行统计
type RunStats struct {
	Finished Finished `xml:"finished"`
	Hosts    HostStats `xml:"hosts"`
}

// Finished 完成信息
type Finished struct {
	Time     string `xml:"time,attr"`
	TimeStr  string `xml:"timestr,attr"`
	Elapsed  string `xml:"elapsed,attr"`
	Summary  string `xml:"summary,attr"`
	Exit     string `xml:"exit,attr"`
}

// HostStats 主机统计
type HostStats struct {
	Up    int `xml:"up,attr"`
	Down  int `xml:"down,attr"`
	Total int `xml:"total,attr"`
}

// NmapScanResult 扫描结果汇总
type NmapScanResult struct {
	Hosts        []NmapHostResult       `json:"hosts"`
	TotalHosts   int                    `json:"total_hosts"`
	AliveHosts   int                    `json:"alive_hosts"`
	ScanDuration float64                `json:"scan_duration"`
	ScanType     string                 `json:"scan_type"`
	Statistics   map[string]interface{} `json:"statistics"`
}

// NmapHostResult 主机扫描结果
type NmapHostResult struct {
	IP        string              `json:"ip"`
	Hostname  string              `json:"hostname,omitempty"`
	Status    string              `json:"status"`
	Ports     []NmapPortResult    `json:"ports"`
	OS        NmapOSResult        `json:"os,omitempty"`
	Scripts   []NmapScriptResult  `json:"scripts,omitempty"`
}

// NmapPortResult 端口扫描结果
type NmapPortResult struct {
	Port     int                `json:"port"`
	Protocol string             `json:"protocol"`
	State    string             `json:"state"`
	Service  NmapServiceResult  `json:"service,omitempty"`
	Scripts  []NmapScriptResult `json:"scripts,omitempty"`
}

// NmapServiceResult 服务识别结果
type NmapServiceResult struct {
	Name      string   `json:"name"`
	Product   string   `json:"product,omitempty"`
	Version   string   `json:"version,omitempty"`
	ExtraInfo string   `json:"extra_info,omitempty"`
	CPE       []string `json:"cpe,omitempty"`
}

// NmapOSResult OS识别结果
type NmapOSResult struct {
	Name     string `json:"name,omitempty"`
	Accuracy int    `json:"accuracy,omitempty"`
	Family   string `json:"family,omitempty"`
	Vendor   string `json:"vendor,omitempty"`
}

// NmapScriptResult 脚本执行结果
type NmapScriptResult struct {
	ID     string                 `json:"id"`
	Output string                 `json:"output"`
	Data   map[string]interface{} `json:"data,omitempty"`
}

// NewNmapScanner 创建Nmap扫描器
func NewNmapScanner(log logger.Logger, config *NmapConfig) *NmapScanner {
	if config == nil {
		config = &NmapConfig{
			NmapPath:    "nmap",
			MaxParallel: 100,
			Timeout:     5 * time.Minute,
		}
	}

	capabilities := []string{
		string(model.TaskTypePortScan),
		string(model.TaskTypeServiceDetect),
		string(model.TaskTypeFingerprintScan),
	}
	
	return &NmapScanner{
		BaseScanner: NewBaseScanner("nmap", "7.94", capabilities, log),
		nmapPath:    config.NmapPath,
		maxParallel: config.MaxParallel,
		timeout:     config.Timeout,
	}
}

// Scan 执行Nmap扫描
func (n *NmapScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	n.logger.Info(fmt.Sprintf("Nmap scanner executing task: %s", task.ID))
	
	// 验证任务类型
	if !n.IsSupported(task.Type) {
		return nil, fmt.Errorf("unsupported task type: %s", task.Type)
	}
	
	// 解析任务配置
	config, err := n.parseTaskConfig(task.Config, task.Type)
	if err != nil {
		return nil, fmt.Errorf("failed to parse task config: %w", err)
	}
	
	// 更新任务状态
	task.Status = model.TaskStatusRunning
	startTime := time.Now()
	task.StartTime = &startTime
	task.Progress = 0
	
	// 执行扫描
	result, err := n.executeScan(ctx, config, task)
	if err != nil {
		task.Status = model.TaskStatusFailed
		task.Error = err.Error()
		return task, err
	}
	
	// 设置扫描结果
	task.Result = map[string]interface{}{
		"scanner":     n.GetName(),
		"version":     n.GetVersion(),
		"scan_result": result,
		"scan_time":   time.Since(startTime).Seconds(),
	}
	
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	completedTime := time.Now()
	task.CompletedTime = &completedTime
	
	n.logger.Info(fmt.Sprintf("Nmap scan completed for task: %s", task.ID))
	return task, nil
}

// parseTaskConfig 解析任务配置
func (n *NmapScanner) parseTaskConfig(config map[string]interface{}, taskType model.TaskType) (*NmapConfig, error) {
	nmapConfig := &NmapConfig{
		NmapPath:    n.nmapPath,
		MaxParallel: n.maxParallel,
		Timeout:     n.timeout,
		Timing:      "T3", // 默认时序
	}

	// 根据任务类型设置默认扫描类型
	switch taskType {
	case model.TaskTypePortScan:
		nmapConfig.ScanType = "syn"
	case model.TaskTypeServiceDetect:
		nmapConfig.ScanType = "version"
	case model.TaskTypeFingerprintScan:
		nmapConfig.ScanType = "script"
		nmapConfig.Scripts = []string{"default", "discovery"}
	}

	// 从任务配置中覆盖默认值
	if scanType, ok := config["scan_type"].(string); ok {
		nmapConfig.ScanType = scanType
	}

	if timing, ok := config["timing"].(string); ok {
		nmapConfig.Timing = timing
	}

	if scripts, ok := config["scripts"].([]interface{}); ok {
		nmapConfig.Scripts = make([]string, len(scripts))
		for i, script := range scripts {
			if s, ok := script.(string); ok {
				nmapConfig.Scripts[i] = s
			}
		}
	}

	if timeout, ok := config["timeout"].(float64); ok {
		nmapConfig.Timeout = time.Duration(timeout) * time.Second
	}

	return nmapConfig, nil
}

// executeScan 执行扫描
func (n *NmapScanner) executeScan(ctx context.Context, config *NmapConfig, task *model.Task) (*NmapScanResult, error) {
	// 获取目标
	target := getStringFromConfig(task.Config, "target", "")
	if target == "" {
		return nil, fmt.Errorf("target is required")
	}

	// 构建Nmap命令
	args := n.buildNmapArgs(target, config, task.Config)

	// 创建带超时的上下文
	scanCtx, cancel := context.WithTimeout(ctx, config.Timeout)
	defer cancel()

	// 执行命令
	cmd := exec.CommandContext(scanCtx, n.nmapPath, args...)

	n.logger.Debug(fmt.Sprintf("Executing nmap command: %s %s", n.nmapPath, strings.Join(args, " ")))

	// 更新进度
	task.Progress = 25

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("nmap execution failed: %w", err)
	}

	// 更新进度
	task.Progress = 75

	// 解析XML输出
	nmapResult, err := n.parseNmapXML(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse nmap output: %w", err)
	}

	// 转换为标准结果格式
	result := n.convertToScanResult(nmapResult, config.ScanType)

	return result, nil
}

// buildNmapArgs 构建Nmap命令参数
func (n *NmapScanner) buildNmapArgs(target string, config *NmapConfig, taskConfig map[string]interface{}) []string {
	args := []string{"-oX", "-"} // XML输出到stdout

	// 添加扫描类型
	switch config.ScanType {
	case "syn":
		args = append(args, "-sS")
	case "tcp":
		args = append(args, "-sT")
	case "udp":
		args = append(args, "-sU")
	case "version":
		args = append(args, "-sV")
	case "script":
		args = append(args, "-sC")
		if len(config.Scripts) > 0 {
			args = append(args, "--script", strings.Join(config.Scripts, ","))
		}
	case "os":
		args = append(args, "-O")
	case "aggressive":
		args = append(args, "-A")
	}

	// 添加时序模板
	if config.Timing != "" {
		args = append(args, "-"+config.Timing)
	}

	// 添加端口范围
	if ports := getPortsFromConfig(taskConfig); len(ports) > 0 {
		portStr := n.formatPorts(ports)
		args = append(args, "-p", portStr)
	}

	// 添加并行度控制
	if config.MaxParallel > 0 {
		args = append(args, "--max-parallelism", strconv.Itoa(config.MaxParallel))
	}

	// 添加其他选项
	args = append(args, "-Pn") // 跳过主机发现
	args = append(args, "--open") // 只显示开放端口

	// 添加目标
	args = append(args, target)

	return args
}

// formatPorts 格式化端口列表
func (n *NmapScanner) formatPorts(ports []int) string {
	if len(ports) == 0 {
		return ""
	}

	var portStrs []string
	for _, port := range ports {
		portStrs = append(portStrs, strconv.Itoa(port))
	}

	return strings.Join(portStrs, ",")
}

// parseNmapXML 解析Nmap XML输出
func (n *NmapScanner) parseNmapXML(xmlData []byte) (*NmapResult, error) {
	var result NmapResult
	if err := xml.Unmarshal(xmlData, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal XML: %w", err)
	}
	return &result, nil
}

// convertToScanResult 转换为标准扫描结果格式
func (n *NmapScanner) convertToScanResult(nmapResult *NmapResult, scanType string) *NmapScanResult {
	result := &NmapScanResult{
		Hosts:      make([]NmapHostResult, 0),
		ScanType:   scanType,
		Statistics: make(map[string]interface{}),
	}

	// 转换主机信息
	for _, host := range nmapResult.Hosts {
		hostResult := n.convertHost(host)
		if hostResult.Status == "up" {
			result.Hosts = append(result.Hosts, hostResult)
		}
	}

	// 计算统计信息
	result.TotalHosts = len(nmapResult.Hosts)
	result.AliveHosts = len(result.Hosts)

	if nmapResult.RunStats.Finished.Elapsed != "" {
		if elapsed, err := strconv.ParseFloat(nmapResult.RunStats.Finished.Elapsed, 64); err == nil {
			result.ScanDuration = elapsed
		}
	}

	// 添加详细统计
	result.Statistics["scanner"] = nmapResult.Scanner
	result.Statistics["version"] = nmapResult.Version
	result.Statistics["args"] = nmapResult.Args
	result.Statistics["scan_info"] = nmapResult.ScanInfo

	return result
}

// convertHost 转换主机信息
func (n *NmapScanner) convertHost(host Host) NmapHostResult {
	hostResult := NmapHostResult{
		Status: host.Status.State,
		Ports:  make([]NmapPortResult, 0),
		Scripts: make([]NmapScriptResult, 0),
	}

	// 获取IP地址
	for _, addr := range host.Address {
		if addr.AddrType == "ipv4" {
			hostResult.IP = addr.Addr
			break
		}
	}

	// 获取主机名
	if len(host.Hostnames.Hostname) > 0 {
		hostResult.Hostname = host.Hostnames.Hostname[0].Name
	}

	// 转换端口信息
	for _, port := range host.Ports.Port {
		portResult := NmapPortResult{
			Port:     port.PortID,
			Protocol: port.Protocol,
			State:    port.State.State,
			Scripts:  make([]NmapScriptResult, 0),
		}

		// 转换服务信息
		if port.Service.Name != "" {
			portResult.Service = NmapServiceResult{
				Name:      port.Service.Name,
				Product:   port.Service.Product,
				Version:   port.Service.Version,
				ExtraInfo: port.Service.ExtraInfo,
			}

			// 转换CPE信息
			for _, cpe := range port.Service.CPE {
				portResult.Service.CPE = append(portResult.Service.CPE, cpe.Value)
			}
		}

		// 转换脚本结果
		for _, script := range port.Scripts {
			portResult.Scripts = append(portResult.Scripts, NmapScriptResult{
				ID:     script.ID,
				Output: script.Output,
			})
		}

		hostResult.Ports = append(hostResult.Ports, portResult)
	}

	// 转换OS信息
	if len(host.OS.OSMatch) > 0 {
		osMatch := host.OS.OSMatch[0]
		if accuracy, err := strconv.Atoi(osMatch.Accuracy); err == nil {
			hostResult.OS = NmapOSResult{
				Name:     osMatch.Name,
				Accuracy: accuracy,
			}

			if len(osMatch.OSClass) > 0 {
				hostResult.OS.Family = osMatch.OSClass[0].OSFamily
				hostResult.OS.Vendor = osMatch.OSClass[0].Vendor
			}
		}
	}

	// 转换主机级脚本结果
	for _, script := range host.Scripts {
		hostResult.Scripts = append(hostResult.Scripts, NmapScriptResult{
			ID:     script.ID,
			Output: script.Output,
		})
	}

	return hostResult
}
