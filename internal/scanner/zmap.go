package scanner

import (
	"bufio"
	"context"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// ZMapScanner ZMap扫描器实现
type ZMapScanner struct {
	*BaseScanner
	zmapPath     string
	maxRate      int    // 最大扫描速率 (packets/sec)
	bandwidth    string // 带宽限制
	cooldownTime int    // 冷却时间 (seconds)
}

// ZMapConfig ZMap配置
type ZMapConfig struct {
	ZMapPath     string `json:"zmap_path"`     // ZMap可执行文件路径
	MaxRate      int    `json:"max_rate"`      // 最大扫描速率
	Bandwidth    string `json:"bandwidth"`     // 带宽限制
	CooldownTime int    `json:"cooldown_time"` // 冷却时间
	Interface    string `json:"interface"`     // 网络接口
	SourceIP     string `json:"source_ip"`     // 源IP地址
}

// ZMapResult ZMap扫描结果
type ZMapResult struct {
	SAddr     string `json:"saddr"`     // 源地址
	DAddr     string `json:"daddr"`     // 目标地址
	Sport     int    `json:"sport"`     // 源端口
	DPort     int    `json:"dport"`     // 目标端口
	SeqNum    int    `json:"seqnum"`    // 序列号
	AckNum    int    `json:"acknum"`    // 确认号
	Window    int    `json:"window"`    // 窗口大小
	Flags     string `json:"flags"`     // TCP标志
	Timestamp int64  `json:"timestamp"` // 时间戳
}

// ZMapScanResult 扫描结果汇总
type ZMapScanResult struct {
	TotalHosts   int                    `json:"total_hosts"`
	AliveHosts   int                    `json:"alive_hosts"`
	OpenPorts    []ZMapPortResult       `json:"open_ports"`
	ScanDuration float64                `json:"scan_duration"`
	PacketsSent  int                    `json:"packets_sent"`
	PacketsRecv  int                    `json:"packets_recv"`
	ScanRate     float64                `json:"scan_rate"`
	Statistics   map[string]interface{} `json:"statistics"`
}

// ZMapPortResult 端口扫描结果
type ZMapPortResult struct {
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	State    string `json:"state"`
	Service  string `json:"service,omitempty"`
}

// NewZMapScanner 创建ZMap扫描器
func NewZMapScanner(log logger.Logger, config *ZMapConfig) *ZMapScanner {
	if config == nil {
		config = &ZMapConfig{
			ZMapPath:     "zmap",
			MaxRate:      10000,
			Bandwidth:    "10M",
			CooldownTime: 8,
		}
	}

	capabilities := []string{string(model.TaskTypePortScan)}

	return &ZMapScanner{
		BaseScanner:  NewBaseScanner("zmap", "2.1.1", capabilities, log),
		zmapPath:     config.ZMapPath,
		maxRate:      config.MaxRate,
		bandwidth:    config.Bandwidth,
		cooldownTime: config.CooldownTime,
	}
}

// Scan 执行ZMap扫描
func (z *ZMapScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	z.logger.Info(fmt.Sprintf("ZMap scanner executing task: %s", task.ID))

	// 验证任务类型
	if !z.IsSupported(task.Type) {
		return nil, fmt.Errorf("unsupported task type: %s", task.Type)
	}

	// 解析任务配置
	config, err := z.parseTaskConfig(task.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse task config: %w", err)
	}

	// 更新任务状态
	task.Status = model.TaskStatusRunning
	startTime := time.Now()
	task.StartTime = &startTime
	task.Progress = 0

	// 执行扫描
	result, err := z.executeScan(ctx, config, task)
	if err != nil {
		task.Status = model.TaskStatusFailed
		task.Error = err.Error()
		return task, err
	}

	// 设置扫描结果
	task.Result = map[string]interface{}{
		"scanner":     z.GetName(),
		"version":     z.GetVersion(),
		"scan_result": result,
		"scan_time":   time.Since(startTime).Seconds(),
	}

	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	completedTime := time.Now()
	task.CompletedTime = &completedTime

	z.logger.Info(fmt.Sprintf("ZMap scan completed for task: %s", task.ID))
	return task, nil
}

// parseTaskConfig 解析任务配置
func (z *ZMapScanner) parseTaskConfig(config map[string]interface{}) (*ZMapConfig, error) {
	zmapConfig := &ZMapConfig{
		ZMapPath:     z.zmapPath,
		MaxRate:      z.maxRate,
		Bandwidth:    z.bandwidth,
		CooldownTime: z.cooldownTime,
	}

	// 从任务配置中覆盖默认值
	if target, ok := config["target"].(string); ok && target != "" {
		zmapConfig.Interface = getStringFromConfig(config, "interface", "")
		zmapConfig.SourceIP = getStringFromConfig(config, "source_ip", "")
	}

	if rate, ok := config["max_rate"].(float64); ok {
		zmapConfig.MaxRate = int(rate)
	}

	if bandwidth, ok := config["bandwidth"].(string); ok {
		zmapConfig.Bandwidth = bandwidth
	}

	return zmapConfig, nil
}

// executeScan 执行扫描
func (z *ZMapScanner) executeScan(ctx context.Context, config *ZMapConfig, task *model.Task) (*ZMapScanResult, error) {
	// 获取目标和端口
	target := getStringFromConfig(task.Config, "target", "")
	if target == "" {
		return nil, fmt.Errorf("target is required")
	}

	ports := getPortsFromConfig(task.Config)
	if len(ports) == 0 {
		ports = []int{80, 443, 22, 21, 25, 53, 110, 143, 993, 995} // 默认端口
	}

	result := &ZMapScanResult{
		OpenPorts:  make([]ZMapPortResult, 0),
		Statistics: make(map[string]interface{}),
	}

	startTime := time.Now()
	totalPorts := len(ports)

	// 逐个端口扫描
	for i, port := range ports {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			// 更新进度
			task.Progress = (i * 100) / totalPorts

			// 执行单端口扫描
			portResults, err := z.scanPort(ctx, target, port, config)
			if err != nil {
				z.logger.Error(fmt.Sprintf("Failed to scan port %d: %v", port, err))
				continue
			}

			result.OpenPorts = append(result.OpenPorts, portResults...)
		}
	}

	// 计算统计信息
	result.ScanDuration = time.Since(startTime).Seconds()
	result.AliveHosts = z.countUniqueHosts(result.OpenPorts)
	result.TotalHosts = result.AliveHosts
	result.ScanRate = float64(totalPorts) / result.ScanDuration

	return result, nil
}

// scanPort 扫描单个端口
func (z *ZMapScanner) scanPort(ctx context.Context, target string, port int, config *ZMapConfig) ([]ZMapPortResult, error) {
	// 构建ZMap命令
	args := z.buildZMapArgs(target, port, config)

	// 创建命令
	cmd := exec.CommandContext(ctx, z.zmapPath, args...)

	// 执行命令并获取输出
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("zmap execution failed: %w", err)
	}

	// 解析输出
	return z.parseZMapOutput(string(output), port)
}

// buildZMapArgs 构建ZMap命令参数
func (z *ZMapScanner) buildZMapArgs(target string, port int, config *ZMapConfig) []string {
	args := []string{
		"-p", strconv.Itoa(port),
		"-r", strconv.Itoa(config.MaxRate),
		"-B", config.Bandwidth,
		"-c", strconv.Itoa(config.CooldownTime),
		"-o", "-", // 输出到stdout
	}

	// 添加接口参数
	if config.Interface != "" {
		args = append(args, "-i", config.Interface)
	}

	// 添加源IP参数
	if config.SourceIP != "" {
		args = append(args, "-S", config.SourceIP)
	}

	// 添加目标
	args = append(args, target)

	return args
}

// parseZMapOutput 解析ZMap输出
func (z *ZMapScanner) parseZMapOutput(output string, port int) ([]ZMapPortResult, error) {
	var results []ZMapPortResult

	scanner := bufio.NewScanner(strings.NewReader(output))
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// ZMap默认输出格式是每行一个IP
		ip := line
		if ip != "" {
			results = append(results, ZMapPortResult{
				IP:       ip,
				Port:     port,
				Protocol: "tcp",
				State:    "open",
			})
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("failed to parse zmap output: %w", err)
	}

	return results, nil
}

// countUniqueHosts 统计唯一主机数量
func (z *ZMapScanner) countUniqueHosts(ports []ZMapPortResult) int {
	hosts := make(map[string]bool)
	for _, port := range ports {
		hosts[port.IP] = true
	}
	return len(hosts)
}

// getStringFromConfig 从配置中获取字符串值
func getStringFromConfig(config map[string]interface{}, key, defaultValue string) string {
	if value, ok := config[key].(string); ok {
		return value
	}
	return defaultValue
}

// getPortsFromConfig 从配置中获取端口列表
func getPortsFromConfig(config map[string]interface{}) []int {
	var ports []int

	if portsInterface, ok := config["ports"]; ok {
		switch v := portsInterface.(type) {
		case []interface{}:
			for _, port := range v {
				if p, ok := port.(float64); ok {
					ports = append(ports, int(p))
				}
			}
		case []int:
			ports = v
		case string:
			// 解析端口范围，如 "80,443,8080-8090"
			ports = parsePortString(v)
		}
	}

	return ports
}

// parsePortString 解析端口字符串
func parsePortString(portStr string) []int {
	var ports []int
	parts := strings.Split(portStr, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "-") {
			// 端口范围
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) == 2 {
				start, err1 := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
				end, err2 := strconv.Atoi(strings.TrimSpace(rangeParts[1]))
				if err1 == nil && err2 == nil && start <= end {
					for i := start; i <= end; i++ {
						ports = append(ports, i)
					}
				}
			}
		} else {
			// 单个端口
			if port, err := strconv.Atoi(part); err == nil {
				ports = append(ports, port)
			}
		}
	}

	return ports
}
