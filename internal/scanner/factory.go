package scanner

import (
	"fmt"
	"sync"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// ScannerFactory 扫描器工厂
type ScannerFactory struct {
	mu       sync.RWMutex
	scanners map[string]Scanner
	logger   logger.Logger
}

// NewScannerFactory 创建扫描器工厂
func NewScannerFactory(log logger.Logger) *ScannerFactory {
	factory := &ScannerFactory{
		scanners: make(map[string]Scanner),
		logger:   log,
	}

	// 注册默认扫描器
	factory.registerDefaultScanners()

	return factory
}

// registerDefaultScanners 注册默认扫描器
func (sf *ScannerFactory) registerDefaultScanners() {
	// 注册ZMap扫描器
	zmapScanner := NewZMapScanner(sf.logger, nil)
	sf.RegisterScanner(zmapScanner)

	// 注册Nmap扫描器
	nmapScanner := NewNmapScanner(sf.logger, nil)
	sf.RegisterScanner(nmapScanner)

	// 注册Nuclei扫描器
	nucleiScanner := NewNucleiScanner(sf.logger, nil)
	sf.RegisterScanner(nucleiScanner)

	// 注册默认扫描器
	defaultScanner := NewScanner(sf.logger)
	sf.RegisterScanner(defaultScanner)

	sf.logger.Info("Default scanners registered successfully")
}

// RegisterScanner 注册扫描器
func (sf *ScannerFactory) RegisterScanner(scanner Scanner) error {
	if scanner == nil {
		return fmt.Errorf("scanner cannot be nil")
	}

	sf.mu.Lock()
	defer sf.mu.Unlock()

	name := scanner.GetName()
	if name == "" {
		return fmt.Errorf("scanner name cannot be empty")
	}

	if _, exists := sf.scanners[name]; exists {
		return fmt.Errorf("scanner %s already registered", name)
	}

	sf.scanners[name] = scanner
	sf.logger.Info(fmt.Sprintf("Scanner registered: %s v%s", name, scanner.GetVersion()))

	return nil
}

// UnregisterScanner 注销扫描器
func (sf *ScannerFactory) UnregisterScanner(name string) error {
	sf.mu.Lock()
	defer sf.mu.Unlock()

	if _, exists := sf.scanners[name]; !exists {
		return fmt.Errorf("scanner %s not found", name)
	}

	delete(sf.scanners, name)
	sf.logger.Info(fmt.Sprintf("Scanner unregistered: %s", name))

	return nil
}

// GetScanner 获取扫描器
func (sf *ScannerFactory) GetScanner(name string) (Scanner, error) {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	scanner, exists := sf.scanners[name]
	if !exists {
		return nil, fmt.Errorf("scanner %s not found", name)
	}

	return scanner, nil
}

// GetScannerByTaskType 根据任务类型获取最佳扫描器
func (sf *ScannerFactory) GetScannerByTaskType(taskType model.TaskType) (Scanner, error) {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	// 定义任务类型到扫描器的优先级映射
	scannerPriority := map[model.TaskType][]string{
		model.TaskTypePortScan:        {"zmap", "nmap", "default"},
		model.TaskTypeServiceDetect:   {"nmap", "default"},
		model.TaskTypeFingerprintScan: {"nmap", "default"},
		model.TaskTypeVulnScan:        {"nuclei", "default"},
		model.TaskTypeAssetDiscovery:  {"nmap", "zmap", "default"},
	}

	// 获取优先级列表
	priorities, exists := scannerPriority[taskType]
	if !exists {
		return nil, fmt.Errorf("no scanner mapping for task type: %s", taskType)
	}

	// 按优先级查找可用的扫描器
	for _, scannerName := range priorities {
		if scanner, exists := sf.scanners[scannerName]; exists && scanner.IsSupported(taskType) {
			return scanner, nil
		}
	}

	return nil, fmt.Errorf("no suitable scanner found for task type: %s", taskType)
}

// ListScanners 列出所有扫描器
func (sf *ScannerFactory) ListScanners() []Scanner {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	scanners := make([]Scanner, 0, len(sf.scanners))
	for _, scanner := range sf.scanners {
		scanners = append(scanners, scanner)
	}

	return scanners
}

// GetScannerInfo 获取扫描器信息
func (sf *ScannerFactory) GetScannerInfo() []ScannerInfo {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	var infos []ScannerInfo
	for _, scanner := range sf.scanners {
		info := ScannerInfo{
			Name:         scanner.GetName(),
			Version:      scanner.GetVersion(),
			Capabilities: scanner.GetCapabilities(),
		}
		infos = append(infos, info)
	}

	return infos
}

// ScannerInfo 扫描器信息
type ScannerInfo struct {
	Name         string   `json:"name"`
	Version      string   `json:"version"`
	Capabilities []string `json:"capabilities"`
}

// ScannerConfig 扫描器配置
type ScannerConfig struct {
	ZMap   *ZMapConfig   `json:"zmap,omitempty"`
	Nmap   *NmapConfig   `json:"nmap,omitempty"`
	Nuclei *NucleiConfig `json:"nuclei,omitempty"`
}

// ConfigurableFactory 可配置的扫描器工厂
type ConfigurableFactory struct {
	*ScannerFactory
	config *ScannerConfig
}

// NewConfigurableFactory 创建可配置的扫描器工厂
func NewConfigurableFactory(log logger.Logger, config *ScannerConfig) *ConfigurableFactory {
	factory := &ConfigurableFactory{
		ScannerFactory: &ScannerFactory{
			scanners: make(map[string]Scanner),
			logger:   log,
		},
		config: config,
	}

	// 使用配置注册扫描器
	factory.registerConfiguredScanners()

	return factory
}

// registerConfiguredScanners 使用配置注册扫描器
func (cf *ConfigurableFactory) registerConfiguredScanners() {
	// 注册ZMap扫描器
	if cf.config != nil && cf.config.ZMap != nil {
		zmapScanner := NewZMapScanner(cf.logger, cf.config.ZMap)
		cf.RegisterScanner(zmapScanner)
	} else {
		zmapScanner := NewZMapScanner(cf.logger, nil)
		cf.RegisterScanner(zmapScanner)
	}

	// 注册Nmap扫描器
	if cf.config != nil && cf.config.Nmap != nil {
		nmapScanner := NewNmapScanner(cf.logger, cf.config.Nmap)
		cf.RegisterScanner(nmapScanner)
	} else {
		nmapScanner := NewNmapScanner(cf.logger, nil)
		cf.RegisterScanner(nmapScanner)
	}

	// 注册Nuclei扫描器
	if cf.config != nil && cf.config.Nuclei != nil {
		nucleiScanner := NewNucleiScanner(cf.logger, cf.config.Nuclei)
		cf.RegisterScanner(nucleiScanner)
	} else {
		nucleiScanner := NewNucleiScanner(cf.logger, nil)
		cf.RegisterScanner(nucleiScanner)
	}

	// 注册默认扫描器
	defaultScanner := NewScanner(cf.logger)
	cf.RegisterScanner(defaultScanner)

	cf.logger.Info("Configured scanners registered successfully")
}

// UpdateConfig 更新扫描器配置
func (cf *ConfigurableFactory) UpdateConfig(config *ScannerConfig) error {
	cf.mu.Lock()
	defer cf.mu.Unlock()

	cf.config = config

	// 清空现有扫描器
	cf.scanners = make(map[string]Scanner)

	// 重新注册扫描器
	cf.registerConfiguredScanners()

	cf.logger.Info("Scanner configuration updated")
	return nil
}

// GetConfig 获取当前配置
func (cf *ConfigurableFactory) GetConfig() *ScannerConfig {
	cf.mu.RLock()
	defer cf.mu.RUnlock()

	return cf.config
}

// ValidateScanner 验证扫描器是否可用
func (sf *ScannerFactory) ValidateScanner(name string) error {
	_, err := sf.GetScanner(name)
	if err != nil {
		return err
	}

	// 这里可以添加更多的验证逻辑
	// 例如检查扫描器的可执行文件是否存在

	sf.logger.Debug(fmt.Sprintf("Scanner %s validation passed", name))
	return nil
}

// GetScannerCapabilities 获取扫描器能力映射
func (sf *ScannerFactory) GetScannerCapabilities() map[string][]string {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	capabilities := make(map[string][]string)
	for name, scanner := range sf.scanners {
		capabilities[name] = scanner.GetCapabilities()
	}

	return capabilities
}

// FindScannersByCapability 根据能力查找扫描器
func (sf *ScannerFactory) FindScannersByCapability(capability string) []Scanner {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	var matchingScanners []Scanner
	for _, scanner := range sf.scanners {
		for _, cap := range scanner.GetCapabilities() {
			if cap == capability {
				matchingScanners = append(matchingScanners, scanner)
				break
			}
		}
	}

	return matchingScanners
}

// GetScannerStats 获取扫描器统计信息
func (sf *ScannerFactory) GetScannerStats() map[string]interface{} {
	sf.mu.RLock()
	defer sf.mu.RUnlock()

	stats := map[string]interface{}{
		"total_scanners": len(sf.scanners),
		"scanners":       make([]map[string]interface{}, 0),
	}

	capabilityCount := make(map[string]int)

	for _, scanner := range sf.scanners {
		scannerInfo := map[string]interface{}{
			"name":         scanner.GetName(),
			"version":      scanner.GetVersion(),
			"capabilities": scanner.GetCapabilities(),
		}
		stats["scanners"] = append(stats["scanners"].([]map[string]interface{}), scannerInfo)

		// 统计能力分布
		for _, cap := range scanner.GetCapabilities() {
			capabilityCount[cap]++
		}
	}

	stats["capability_distribution"] = capabilityCount

	return stats
}
