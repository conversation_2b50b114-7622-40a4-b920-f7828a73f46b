package scanner

import (
	"context"
	"fmt"

	"golem-backend/internal/model"
	"golem-backend/pkg/logger"
)

// Scanner 扫描器接口
type Scanner interface {
	// Scan 执行扫描
	Scan(ctx context.Context, task *model.Task) (*model.Task, error)
	
	// GetCapabilities 获取扫描器能力
	GetCapabilities() []string
	
	// IsSupported 检查是否支持指定任务类型
	IsSupported(taskType model.TaskType) bool
	
	// GetName 获取扫描器名称
	GetName() string
	
	// GetVersion 获取扫描器版本
	GetVersion() string
}

// ScannerManager 扫描器管理器接口
type ScannerManager interface {
	// RegisterScanner 注册扫描器
	RegisterScanner(scanner Scanner) error
	
	// UnregisterScanner 注销扫描器
	UnregisterScanner(name string) error
	
	// GetScanner 获取扫描器
	GetScanner(name string) (Scanner, error)
	
	// GetScannerByTaskType 根据任务类型获取扫描器
	GetScannerByTaskType(taskType model.TaskType) (Scanner, error)
	
	// ListScanners 列出所有扫描器
	ListScanners() []Scanner
}

// BaseScanner 基础扫描器实现
type BaseScanner struct {
	name         string
	version      string
	capabilities []string
	logger       logger.Logger
}

// NewBaseScanner 创建基础扫描器
func NewBaseScanner(name, version string, capabilities []string, log logger.Logger) *BaseScanner {
	return &BaseScanner{
		name:         name,
		version:      version,
		capabilities: capabilities,
		logger:       log,
	}
}

// GetName 获取扫描器名称
func (bs *BaseScanner) GetName() string {
	return bs.name
}

// GetVersion 获取扫描器版本
func (bs *BaseScanner) GetVersion() string {
	return bs.version
}

// GetCapabilities 获取扫描器能力
func (bs *BaseScanner) GetCapabilities() []string {
	return bs.capabilities
}

// IsSupported 检查是否支持指定任务类型
func (bs *BaseScanner) IsSupported(taskType model.TaskType) bool {
	for _, capability := range bs.capabilities {
		if capability == string(taskType) {
			return true
		}
	}
	return false
}

// scannerManager 扫描器管理器实现
type scannerManager struct {
	scanners map[string]Scanner
	logger   logger.Logger
}

// NewScannerManager 创建扫描器管理器
func NewScannerManager(log logger.Logger) ScannerManager {
	return &scannerManager{
		scanners: make(map[string]Scanner),
		logger:   log,
	}
}

// RegisterScanner 注册扫描器
func (sm *scannerManager) RegisterScanner(scanner Scanner) error {
	if scanner == nil {
		return fmt.Errorf("scanner cannot be nil")
	}
	
	name := scanner.GetName()
	if name == "" {
		return fmt.Errorf("scanner name cannot be empty")
	}
	
	if _, exists := sm.scanners[name]; exists {
		return fmt.Errorf("scanner %s already registered", name)
	}
	
	sm.scanners[name] = scanner
	sm.logger.Info(fmt.Sprintf("Scanner registered: %s v%s", name, scanner.GetVersion()))
	
	return nil
}

// UnregisterScanner 注销扫描器
func (sm *scannerManager) UnregisterScanner(name string) error {
	if _, exists := sm.scanners[name]; !exists {
		return fmt.Errorf("scanner %s not found", name)
	}
	
	delete(sm.scanners, name)
	sm.logger.Info(fmt.Sprintf("Scanner unregistered: %s", name))
	
	return nil
}

// GetScanner 获取扫描器
func (sm *scannerManager) GetScanner(name string) (Scanner, error) {
	scanner, exists := sm.scanners[name]
	if !exists {
		return nil, fmt.Errorf("scanner %s not found", name)
	}
	
	return scanner, nil
}

// GetScannerByTaskType 根据任务类型获取扫描器
func (sm *scannerManager) GetScannerByTaskType(taskType model.TaskType) (Scanner, error) {
	for _, scanner := range sm.scanners {
		if scanner.IsSupported(taskType) {
			return scanner, nil
		}
	}
	
	return nil, fmt.Errorf("no scanner found for task type: %s", taskType)
}

// ListScanners 列出所有扫描器
func (sm *scannerManager) ListScanners() []Scanner {
	scanners := make([]Scanner, 0, len(sm.scanners))
	for _, scanner := range sm.scanners {
		scanners = append(scanners, scanner)
	}
	return scanners
}

// DefaultScanner 默认扫描器实现
type DefaultScanner struct {
	*BaseScanner
}

// NewScanner 创建默认扫描器
func NewScanner(log logger.Logger) Scanner {
	capabilities := []string{
		string(model.TaskTypePortScan),
		string(model.TaskTypeServiceDetect),
		string(model.TaskTypeVulnScan),
		string(model.TaskTypeFingerprintScan),
		string(model.TaskTypeAssetDiscovery),
	}
	
	return &DefaultScanner{
		BaseScanner: NewBaseScanner("default", "1.0.0", capabilities, log),
	}
}

// Scan 执行扫描
func (ds *DefaultScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	ds.logger.Info(fmt.Sprintf("Default scanner executing task: %s", task.ID))
	
	// 这里应该实现具体的扫描逻辑
	// 目前返回一个模拟结果
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	task.Result = map[string]interface{}{
		"scanner": ds.GetName(),
		"version": ds.GetVersion(),
		"message": "Scan completed successfully",
	}
	
	return task, nil
}

// PortScanner 端口扫描器
type PortScanner struct {
	*BaseScanner
}

// NewPortScanner 创建端口扫描器
func NewPortScanner(log logger.Logger) Scanner {
	capabilities := []string{string(model.TaskTypePortScan)}
	return &PortScanner{
		BaseScanner: NewBaseScanner("port_scanner", "1.0.0", capabilities, log),
	}
}

// Scan 执行端口扫描
func (ps *PortScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	ps.logger.Info(fmt.Sprintf("Port scanner executing task: %s", task.ID))
	
	// 模拟端口扫描逻辑
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	task.Result = map[string]interface{}{
		"open_ports": []int{22, 80, 443, 8080},
		"closed_ports": []int{21, 23, 25, 53},
		"filtered_ports": []int{135, 139, 445},
	}
	
	return task, nil
}

// ServiceScanner 服务识别扫描器
type ServiceScanner struct {
	*BaseScanner
}

// NewServiceScanner 创建服务识别扫描器
func NewServiceScanner(log logger.Logger) Scanner {
	capabilities := []string{string(model.TaskTypeServiceDetect)}
	return &ServiceScanner{
		BaseScanner: NewBaseScanner("service_scanner", "1.0.0", capabilities, log),
	}
}

// Scan 执行服务识别
func (ss *ServiceScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	ss.logger.Info(fmt.Sprintf("Service scanner executing task: %s", task.ID))
	
	// 模拟服务识别逻辑
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	task.Result = map[string]interface{}{
		"services": []map[string]interface{}{
			{"port": 22, "service": "ssh", "version": "OpenSSH 8.0", "state": "open"},
			{"port": 80, "service": "http", "version": "nginx/1.18.0", "state": "open"},
			{"port": 443, "service": "https", "version": "nginx/1.18.0", "state": "open"},
		},
	}
	
	return task, nil
}

// VulnScanner 漏洞扫描器
type VulnScanner struct {
	*BaseScanner
}

// NewVulnScanner 创建漏洞扫描器
func NewVulnScanner(log logger.Logger) Scanner {
	capabilities := []string{string(model.TaskTypeVulnScan)}
	return &VulnScanner{
		BaseScanner: NewBaseScanner("vuln_scanner", "1.0.0", capabilities, log),
	}
}

// Scan 执行漏洞扫描
func (vs *VulnScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
	vs.logger.Info(fmt.Sprintf("Vulnerability scanner executing task: %s", task.ID))
	
	// 模拟漏洞扫描逻辑
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	task.Result = map[string]interface{}{
		"vulnerabilities": []map[string]interface{}{
			{
				"cve": "CVE-2021-44228",
				"severity": "critical",
				"description": "Apache Log4j2 Remote Code Execution",
				"cvss": 10.0,
			},
			{
				"cve": "CVE-2021-34527",
				"severity": "high",
				"description": "Windows Print Spooler Remote Code Execution",
				"cvss": 8.8,
			},
		},
	}
	
	return task, nil
}
