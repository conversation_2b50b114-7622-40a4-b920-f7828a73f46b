package engine

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net"
	"runtime"
	"time"

	"golem-backend/internal/model"
)

// generateNodeID 生成节点ID
func generateNodeID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳
		return fmt.Sprintf("node_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// getLocalIP 获取本地IP地址
func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()
	
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

// getCPUUsage 获取CPU使用率
func getCPUUsage() float64 {
	// 这里应该实现真实的CPU使用率获取逻辑
	// 为了演示，返回一个模拟值
	return 25.5
}

// getMemoryUsage 获取内存使用率
func getMemoryUsage() float64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 计算内存使用率（简化版本）
	used := float64(m.Alloc)
	total := float64(m.Sys)
	
	if total == 0 {
		return 0
	}
	
	return (used / total) * 100
}

// parseTask 解析任务数据
func parseTask(data []byte) (*model.Task, error) {
	var task model.Task
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, fmt.Errorf("failed to unmarshal task: %w", err)
	}
	return &task, nil
}

// serializeTask 序列化任务数据
func serializeTask(task *model.Task) ([]byte, error) {
	data, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal task: %w", err)
	}
	return data, nil
}

// validateTask 验证任务数据
func validateTask(task *model.Task) error {
	if task == nil {
		return fmt.Errorf("task cannot be nil")
	}
	
	if task.ID == "" {
		return fmt.Errorf("task ID cannot be empty")
	}
	
	if task.Type == "" {
		return fmt.Errorf("task type cannot be empty")
	}
	
	if task.AssetID == "" {
		return fmt.Errorf("asset ID cannot be empty")
	}
	
	// 验证任务类型
	validTypes := map[model.TaskType]bool{
		model.TaskTypePortScan:        true,
		model.TaskTypeServiceDetect:   true,
		model.TaskTypeVulnScan:        true,
		model.TaskTypeFingerprintScan: true,
		model.TaskTypeAssetDiscovery:  true,
	}
	
	if !validTypes[task.Type] {
		return fmt.Errorf("invalid task type: %s", task.Type)
	}
	
	return nil
}

// generateTaskID 生成任务ID
func generateTaskID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("task_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("task_%s", hex.EncodeToString(bytes))
}

// generateAssetID 生成资产ID
func generateAssetID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("asset_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("asset_%s", hex.EncodeToString(bytes))
}

// isValidIP 验证IP地址
func isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// isValidPort 验证端口号
func isValidPort(port int) bool {
	return port > 0 && port <= 65535
}

// formatDuration 格式化持续时间
func formatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%.2fms", float64(d.Nanoseconds())/1e6)
	} else if d < time.Minute {
		return fmt.Sprintf("%.2fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.2fm", d.Minutes())
	} else {
		return fmt.Sprintf("%.2fh", d.Hours())
	}
}

// calculateProgress 计算进度百分比
func calculateProgress(current, total int) int {
	if total == 0 {
		return 0
	}
	progress := (current * 100) / total
	if progress > 100 {
		progress = 100
	}
	return progress
}

// mergeConfig 合并配置
func mergeConfig(base, override map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	
	// 复制基础配置
	for k, v := range base {
		result[k] = v
	}
	
	// 覆盖配置
	for k, v := range override {
		result[k] = v
	}
	
	return result
}

// deepCopy 深拷贝
func deepCopy(src interface{}) (interface{}, error) {
	data, err := json.Marshal(src)
	if err != nil {
		return nil, err
	}
	
	var dst interface{}
	if err := json.Unmarshal(data, &dst); err != nil {
		return nil, err
	}
	
	return dst, nil
}

// retry 重试机制
func retry(attempts int, delay time.Duration, fn func() error) error {
	var err error
	for i := 0; i < attempts; i++ {
		if err = fn(); err == nil {
			return nil
		}
		
		if i < attempts-1 {
			time.Sleep(delay)
			delay *= 2 // 指数退避
		}
	}
	return err
}

// timeout 超时执行
func timeout(duration time.Duration, fn func() error) error {
	done := make(chan error, 1)
	
	go func() {
		done <- fn()
	}()
	
	select {
	case err := <-done:
		return err
	case <-time.After(duration):
		return fmt.Errorf("operation timed out after %v", duration)
	}
}

// contains 检查切片是否包含元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// unique 去重切片
func unique(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// min 获取最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// max 获取最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// clamp 限制值在范围内
func clamp(value, min, max int) int {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}

// formatBytes 格式化字节数
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	
	div, exp := uint64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f %s", float64(bytes)/float64(div), units[exp])
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// getCurrentTimestampMs 获取当前毫秒时间戳
func getCurrentTimestampMs() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// parseTimestamp 解析时间戳
func parseTimestamp(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// parseTimestampMs 解析毫秒时间戳
func parseTimestampMs(timestamp int64) time.Time {
	return time.Unix(0, timestamp*int64(time.Millisecond))
}
