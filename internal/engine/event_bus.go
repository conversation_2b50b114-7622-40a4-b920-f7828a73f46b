package engine

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"time"

	"golem-backend/pkg/logger"
)

// eventBus 事件总线实现
type eventBus struct {
	mu          sync.RWMutex
	subscribers map[string][]EventHandler
	logger      logger.Logger
	bufferSize  int
}

// NewEventBus 创建事件总线
func NewEventBus(log logger.Logger) EventBus {
	return &eventBus{
		subscribers: make(map[string][]EventHandler),
		logger:      log,
		bufferSize:  100, // 默认缓冲区大小
	}
}

// Publish 发布事件
func (eb *eventBus) Publish(ctx context.Context, topic string, event interface{}) error {
	eb.mu.RLock()
	handlers := eb.subscribers[topic]
	eb.mu.RUnlock()
	
	if len(handlers) == 0 {
		eb.logger.Debug(fmt.Sprintf("No subscribers for topic: %s", topic))
		return nil
	}
	
	eb.logger.Debug(fmt.Sprintf("Publishing event to topic: %s, subscribers: %d", topic, len(handlers)))
	
	// 异步处理事件，避免阻塞发布者
	go eb.processEvent(ctx, topic, event, handlers)
	
	return nil
}

// processEvent 处理事件
func (eb *eventBus) processEvent(ctx context.Context, topic string, event interface{}, handlers []EventHandler) {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	
	// 并发处理所有订阅者
	var wg sync.WaitGroup
	for _, handler := range handlers {
		wg.Add(1)
		go func(h EventHandler) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					eb.logger.Error(fmt.Sprintf("Event handler panic for topic %s: %v", topic, r))
				}
			}()
			
			if err := h(ctx, event); err != nil {
				eb.logger.Error(fmt.Sprintf("Event handler error for topic %s: %v", topic, err))
			}
		}(handler)
	}
	
	// 等待所有处理器完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		eb.logger.Debug(fmt.Sprintf("All event handlers completed for topic: %s", topic))
	case <-ctx.Done():
		eb.logger.Warn(fmt.Sprintf("Event processing timeout for topic: %s", topic))
	}
}

// Subscribe 订阅事件
func (eb *eventBus) Subscribe(ctx context.Context, topic string, handler EventHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	eb.mu.Lock()
	defer eb.mu.Unlock()
	
	eb.subscribers[topic] = append(eb.subscribers[topic], handler)
	eb.logger.Debug(fmt.Sprintf("Subscribed to topic: %s, total subscribers: %d", 
		topic, len(eb.subscribers[topic])))
	
	return nil
}

// Unsubscribe 取消订阅
func (eb *eventBus) Unsubscribe(ctx context.Context, topic string, handler EventHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	eb.mu.Lock()
	defer eb.mu.Unlock()
	
	handlers := eb.subscribers[topic]
	if len(handlers) == 0 {
		return fmt.Errorf("no subscribers for topic: %s", topic)
	}
	
	// 查找并移除处理器
	handlerPtr := reflect.ValueOf(handler).Pointer()
	for i, h := range handlers {
		if reflect.ValueOf(h).Pointer() == handlerPtr {
			// 移除处理器
			eb.subscribers[topic] = append(handlers[:i], handlers[i+1:]...)
			eb.logger.Debug(fmt.Sprintf("Unsubscribed from topic: %s, remaining subscribers: %d", 
				topic, len(eb.subscribers[topic])))
			return nil
		}
	}
	
	return fmt.Errorf("handler not found for topic: %s", topic)
}

// GetSubscriberCount 获取订阅者数量
func (eb *eventBus) GetSubscriberCount(topic string) int {
	eb.mu.RLock()
	defer eb.mu.RUnlock()
	
	return len(eb.subscribers[topic])
}

// GetTopics 获取所有主题
func (eb *eventBus) GetTopics() []string {
	eb.mu.RLock()
	defer eb.mu.RUnlock()
	
	topics := make([]string, 0, len(eb.subscribers))
	for topic := range eb.subscribers {
		topics = append(topics, topic)
	}
	
	return topics
}

// Clear 清空所有订阅
func (eb *eventBus) Clear() {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	
	eb.subscribers = make(map[string][]EventHandler)
	eb.logger.Info("Event bus cleared")
}

// Event 事件结构
type Event struct {
	ID        string                 `json:"id"`
	Topic     string                 `json:"topic"`
	Data      interface{}            `json:"data"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewEvent 创建新事件
func NewEvent(topic string, data interface{}) *Event {
	return &Event{
		ID:        generateEventID(),
		Topic:     topic,
		Data:      data,
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
	}
}

// SetMetadata 设置元数据
func (e *Event) SetMetadata(key string, value interface{}) {
	e.Metadata[key] = value
}

// GetMetadata 获取元数据
func (e *Event) GetMetadata(key string) interface{} {
	return e.Metadata[key]
}

// generateEventID 生成事件ID
func generateEventID() string {
	return fmt.Sprintf("event_%d", time.Now().UnixNano())
}

// TypedEventBus 类型化事件总线
type TypedEventBus[T any] struct {
	eventBus EventBus
	topic    string
}

// NewTypedEventBus 创建类型化事件总线
func NewTypedEventBus[T any](eventBus EventBus, topic string) *TypedEventBus[T] {
	return &TypedEventBus[T]{
		eventBus: eventBus,
		topic:    topic,
	}
}

// Publish 发布类型化事件
func (teb *TypedEventBus[T]) Publish(ctx context.Context, event T) error {
	return teb.eventBus.Publish(ctx, teb.topic, event)
}

// Subscribe 订阅类型化事件
func (teb *TypedEventBus[T]) Subscribe(ctx context.Context, handler func(ctx context.Context, event T) error) error {
	wrappedHandler := func(ctx context.Context, event interface{}) error {
		if typedEvent, ok := event.(T); ok {
			return handler(ctx, typedEvent)
		}
		return fmt.Errorf("invalid event type for topic %s", teb.topic)
	}
	
	return teb.eventBus.Subscribe(ctx, teb.topic, wrappedHandler)
}

// EventFilter 事件过滤器
type EventFilter func(event interface{}) bool

// FilteredEventBus 带过滤器的事件总线
type FilteredEventBus struct {
	eventBus EventBus
	filters  map[string][]EventFilter
	mu       sync.RWMutex
}

// NewFilteredEventBus 创建带过滤器的事件总线
func NewFilteredEventBus(eventBus EventBus) *FilteredEventBus {
	return &FilteredEventBus{
		eventBus: eventBus,
		filters:  make(map[string][]EventFilter),
	}
}

// AddFilter 添加过滤器
func (feb *FilteredEventBus) AddFilter(topic string, filter EventFilter) {
	feb.mu.Lock()
	defer feb.mu.Unlock()
	
	feb.filters[topic] = append(feb.filters[topic], filter)
}

// Publish 发布事件（带过滤）
func (feb *FilteredEventBus) Publish(ctx context.Context, topic string, event interface{}) error {
	feb.mu.RLock()
	filters := feb.filters[topic]
	feb.mu.RUnlock()
	
	// 应用过滤器
	for _, filter := range filters {
		if !filter(event) {
			return nil // 事件被过滤掉
		}
	}
	
	return feb.eventBus.Publish(ctx, topic, event)
}

// Subscribe 订阅事件
func (feb *FilteredEventBus) Subscribe(ctx context.Context, topic string, handler EventHandler) error {
	return feb.eventBus.Subscribe(ctx, topic, handler)
}

// Unsubscribe 取消订阅
func (feb *FilteredEventBus) Unsubscribe(ctx context.Context, topic string, handler EventHandler) error {
	return feb.eventBus.Unsubscribe(ctx, topic, handler)
}
