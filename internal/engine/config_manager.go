package engine

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"

	"golem-backend/pkg/config"
	"golem-backend/pkg/logger"
)

// configManager 配置管理器实现
type configManager struct {
	mu        sync.RWMutex
	config    *config.Config
	logger    logger.Logger
	watchers  map[string][]chan interface{}
	values    map[string]interface{}
}

// NewConfigManager 创建配置管理器
func NewConfigManager(cfg *config.Config, log logger.Logger) ConfigManager {
	cm := &configManager{
		config:   cfg,
		logger:   log,
		watchers: make(map[string][]chan interface{}),
		values:   make(map[string]interface{}),
	}
	
	// 初始化配置值
	cm.initializeValues()
	
	return cm
}

// initializeValues 初始化配置值
func (cm *configManager) initializeValues() {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 使用反射遍历配置结构体
	cm.flattenStruct("", reflect.ValueOf(cm.config).Elem())
}

// flattenStruct 扁平化结构体
func (cm *configManager) flattenStruct(prefix string, v reflect.Value) {
	t := v.Type()
	
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 构建键名
		key := fieldType.Name
		if prefix != "" {
			key = prefix + "." + key
		}
		
		// 转换为小写并用下划线分隔
		key = strings.ToLower(key)
		
		switch field.Kind() {
		case reflect.Struct:
			// 递归处理嵌套结构体
			cm.flattenStruct(key, field)
		default:
			// 存储基本类型值
			if field.CanInterface() {
				cm.values[key] = field.Interface()
			}
		}
	}
}

// Get 获取配置值
func (cm *configManager) Get(key string) interface{} {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	// 标准化键名
	key = strings.ToLower(key)
	
	if value, exists := cm.values[key]; exists {
		return value
	}
	
	// 尝试通过点分隔符查找嵌套值
	return cm.getNestedValue(key)
}

// getNestedValue 获取嵌套值
func (cm *configManager) getNestedValue(key string) interface{} {
	parts := strings.Split(key, ".")
	current := reflect.ValueOf(cm.config).Elem()
	
	for _, part := range parts {
		// 查找字段
		field := current.FieldByNameFunc(func(name string) bool {
			return strings.ToLower(name) == strings.ToLower(part)
		})
		
		if !field.IsValid() {
			return nil
		}
		
		current = field
	}
	
	if current.CanInterface() {
		return current.Interface()
	}
	
	return nil
}

// Set 设置配置值
func (cm *configManager) Set(key string, value interface{}) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 标准化键名
	key = strings.ToLower(key)
	
	// 保存旧值用于比较
	oldValue := cm.values[key]
	
	// 设置新值
	cm.values[key] = value
	
	// 如果值发生变化，通知观察者
	if !reflect.DeepEqual(oldValue, value) {
		cm.notifyWatchers(key, value)
	}
	
	cm.logger.Debug(fmt.Sprintf("Config value set: %s = %v", key, value))
	return nil
}

// Watch 监听配置变化
func (cm *configManager) Watch(key string) (<-chan interface{}, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 标准化键名
	key = strings.ToLower(key)
	
	// 创建通道
	ch := make(chan interface{}, 10)
	
	// 添加到观察者列表
	cm.watchers[key] = append(cm.watchers[key], ch)
	
	cm.logger.Debug(fmt.Sprintf("Added watcher for config key: %s", key))
	return ch, nil
}

// notifyWatchers 通知观察者
func (cm *configManager) notifyWatchers(key string, value interface{}) {
	watchers := cm.watchers[key]
	
	for _, ch := range watchers {
		select {
		case ch <- value:
		default:
			// 通道已满，跳过
			cm.logger.Warn(fmt.Sprintf("Watcher channel full for key: %s", key))
		}
	}
}

// Reload 重新加载配置
func (cm *configManager) Reload(ctx context.Context) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	cm.logger.Info("Reloading configuration...")
	
	// 重新加载配置文件
	// 这里可以实现从文件、环境变量或远程配置中心重新加载
	
	// 重新初始化配置值
	oldValues := make(map[string]interface{})
	for k, v := range cm.values {
		oldValues[k] = v
	}
	
	cm.values = make(map[string]interface{})
	cm.initializeValues()
	
	// 检查变化并通知观察者
	for key, newValue := range cm.values {
		if oldValue, exists := oldValues[key]; !exists || !reflect.DeepEqual(oldValue, newValue) {
			cm.notifyWatchers(key, newValue)
		}
	}
	
	cm.logger.Info("Configuration reloaded successfully")
	return nil
}

// GetString 获取字符串配置值
func (cm *configManager) GetString(key string) string {
	if value := cm.Get(key); value != nil {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetInt 获取整数配置值
func (cm *configManager) GetInt(key string) int {
	if value := cm.Get(key); value != nil {
		if i, ok := value.(int); ok {
			return i
		}
	}
	return 0
}

// GetBool 获取布尔配置值
func (cm *configManager) GetBool(key string) bool {
	if value := cm.Get(key); value != nil {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return false
}

// GetFloat64 获取浮点数配置值
func (cm *configManager) GetFloat64(key string) float64 {
	if value := cm.Get(key); value != nil {
		if f, ok := value.(float64); ok {
			return f
		}
	}
	return 0.0
}

// GetStringSlice 获取字符串切片配置值
func (cm *configManager) GetStringSlice(key string) []string {
	if value := cm.Get(key); value != nil {
		if slice, ok := value.([]string); ok {
			return slice
		}
	}
	return nil
}

// GetStringMap 获取字符串映射配置值
func (cm *configManager) GetStringMap(key string) map[string]interface{} {
	if value := cm.Get(key); value != nil {
		if m, ok := value.(map[string]interface{}); ok {
			return m
		}
	}
	return nil
}

// GetAllKeys 获取所有配置键
func (cm *configManager) GetAllKeys() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	keys := make([]string, 0, len(cm.values))
	for key := range cm.values {
		keys = append(keys, key)
	}
	
	return keys
}

// IsSet 检查配置键是否存在
func (cm *configManager) IsSet(key string) bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	key = strings.ToLower(key)
	_, exists := cm.values[key]
	return exists
}

// Close 关闭配置管理器
func (cm *configManager) Close() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 关闭所有观察者通道
	for key, watchers := range cm.watchers {
		for _, ch := range watchers {
			close(ch)
		}
		delete(cm.watchers, key)
	}
	
	cm.logger.Info("Configuration manager closed")
	return nil
}
