package engine

import (
	"context"
	"fmt"
	"time"

	"golem-backend/internal/model"
	"golem-backend/internal/registry"
	"golem-backend/internal/scanner"
	"golem-backend/internal/scheduler"
	"golem-backend/pkg/cli"
	"golem-backend/pkg/config"
	"golem-backend/pkg/logger"
	"golem-backend/pkg/storage/queue/rabbitmq"
)

// AgentEngine 代理节点引擎
type AgentEngine struct {
	*CoreEngine
	option    *cli.Option
	config    *config.Config
	logger    logger.Logger
	
	// 核心组件
	registry  registry.Registry
	scheduler scheduler.Scheduler
	scanner   scanner.Scanner
	rabbitmq  *rabbitmq.Client
	
	// 节点信息
	nodeInfo *model.Node
	
	// 任务处理
	taskChan   chan *model.Task
	stopChan   chan struct{}
	workerPool *WorkerPool
}

// NewAgentEngine 创建代理节点引擎
func NewAgentEngine() (*AgentEngine, error) {
	// 解析命令行参数
	option := cli.Parse()
	
	// 加载配置
	cfg, err := config.LoadConfig(option.ConfigFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	
	// 初始化日志
	log := logger.NewLogger(&cfg.Logger)
	
	// 创建核心引擎
	coreEngine := NewCoreEngine(cfg, log)
	
	engine := &AgentEngine{
		CoreEngine: coreEngine,
		option:     option,
		config:     cfg,
		logger:     log,
		taskChan:   make(chan *model.Task, 100),
		stopChan:   make(chan struct{}),
	}
	
	// 初始化组件
	if err := engine.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}
	
	// 初始化节点信息
	if err := engine.initializeNodeInfo(); err != nil {
		return nil, fmt.Errorf("failed to initialize node info: %w", err)
	}
	
	return engine, nil
}

// initializeComponents 初始化组件
func (a *AgentEngine) initializeComponents() error {
	var err error
	
	// 初始化RabbitMQ
	if a.config.Rabbitmq.Enable {
		if a.rabbitmq, err = rabbitmq.NewClient(&a.config.Rabbitmq); err != nil {
			return fmt.Errorf("failed to initialize RabbitMQ: %w", err)
		}
	}
	
	// 初始化注册中心
	a.registry = registry.NewRegistry(a.logger)
	
	// 初始化调度器
	a.scheduler = scheduler.NewScheduler(a.logger)
	
	// 初始化扫描器
	a.scanner = scanner.NewScanner(a.logger)
	
	// 初始化工作池
	a.workerPool = NewWorkerPool(10, a.logger) // 默认10个工作协程
	
	a.logger.Info("Agent components initialized successfully")
	return nil
}

// initializeNodeInfo 初始化节点信息
func (a *AgentEngine) initializeNodeInfo() error {
	a.nodeInfo = &model.Node{
		ID:           generateNodeID(),
		Name:         fmt.Sprintf("agent-%s", generateNodeID()[:8]),
		Type:         model.NodeTypeAgent,
		Status:       model.NodeStatusOffline,
		IPAddress:    getLocalIP(),
		Port:         8080, // 默认端口
		Version:      "1.0.0",
		Tags:         []string{"agent", "scanner"},
		Config:       make(map[string]interface{}),
		Capabilities: []string{"port_scan", "service_detect", "vuln_scan"},
		CurrentTasks: 0,
		MaxTasks:     10,
		CPUUsage:     0.0,
		MemoryUsage:  0.0,
		RegisterTime: time.Now(),
		UpdatedTime:  time.Now(),
	}
	
	a.logger.Info(fmt.Sprintf("Node info initialized: %s", a.nodeInfo.ID))
	return nil
}

// Start 启动代理引擎
func (a *AgentEngine) Start(ctx context.Context) error {
	a.logger.Info("Starting agent engine...")
	
	// 启动核心引擎
	if err := a.CoreEngine.Start(ctx); err != nil {
		return fmt.Errorf("failed to start core engine: %w", err)
	}
	
	// 注册节点
	if err := a.registerNode(ctx); err != nil {
		return fmt.Errorf("failed to register node: %w", err)
	}
	
	// 启动工作池
	a.workerPool.Start(ctx)
	
	// 启动任务处理循环
	go a.taskProcessingLoop(ctx)
	
	// 启动心跳循环
	go a.heartbeatLoop(ctx)
	
	// 启动任务订阅
	if a.rabbitmq != nil {
		go a.subscribeToTasks(ctx)
	}
	
	a.nodeInfo.Status = model.NodeStatusOnline
	a.logger.Info("Agent engine started successfully")
	
	return nil
}

// Stop 停止代理引擎
func (a *AgentEngine) Stop(ctx context.Context) error {
	a.logger.Info("Stopping agent engine...")
	
	// 更新节点状态
	a.nodeInfo.Status = model.NodeStatusOffline
	
	// 停止任务处理
	close(a.stopChan)
	
	// 停止工作池
	a.workerPool.Stop(ctx)
	
	// 注销节点
	if err := a.unregisterNode(ctx); err != nil {
		a.logger.Error(fmt.Sprintf("Failed to unregister node: %v", err))
	}
	
	// 关闭RabbitMQ连接
	if a.rabbitmq != nil {
		if err := a.rabbitmq.Close(); err != nil {
			a.logger.Error(fmt.Sprintf("RabbitMQ close error: %v", err))
		}
	}
	
	// 停止核心引擎
	if err := a.CoreEngine.Stop(ctx); err != nil {
		a.logger.Error(fmt.Sprintf("Core engine stop error: %v", err))
	}
	
	a.logger.Info("Agent engine stopped")
	return nil
}

// registerNode 注册节点
func (a *AgentEngine) registerNode(ctx context.Context) error {
	return a.registry.RegisterNode(ctx, a.nodeInfo)
}

// unregisterNode 注销节点
func (a *AgentEngine) unregisterNode(ctx context.Context) error {
	return a.registry.UnregisterNode(ctx, a.nodeInfo.ID)
}

// taskProcessingLoop 任务处理循环
func (a *AgentEngine) taskProcessingLoop(ctx context.Context) {
	for {
		select {
		case task := <-a.taskChan:
			// 提交任务到工作池
			a.workerPool.Submit(&TaskJob{
				task:    task,
				scanner: a.scanner,
				logger:  a.logger,
			})
			
		case <-a.stopChan:
			return
			
		case <-ctx.Done():
			return
		}
	}
}

// heartbeatLoop 心跳循环
func (a *AgentEngine) heartbeatLoop(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// 更新节点状态
			a.updateNodeStatus()
			
			// 发送心跳
			if err := a.registry.Heartbeat(ctx, a.nodeInfo.ID); err != nil {
				a.logger.Error(fmt.Sprintf("Heartbeat failed: %v", err))
			}
			
		case <-a.stopChan:
			return
			
		case <-ctx.Done():
			return
		}
	}
}

// subscribeToTasks 订阅任务
func (a *AgentEngine) subscribeToTasks(ctx context.Context) {
	queueName := fmt.Sprintf("agent_%s", a.nodeInfo.ID)
	
	handler := func(data []byte) error {
		// 解析任务
		task, err := parseTask(data)
		if err != nil {
			return fmt.Errorf("failed to parse task: %w", err)
		}
		
		// 将任务发送到处理通道
		select {
		case a.taskChan <- task:
			a.logger.Debug(fmt.Sprintf("Task received: %s", task.ID))
		default:
			a.logger.Warn("Task channel full, dropping task")
		}
		
		return nil
	}
	
	if err := a.rabbitmq.Subscribe(ctx, queueName, handler); err != nil {
		a.logger.Error(fmt.Sprintf("Failed to subscribe to tasks: %v", err))
	}
}

// updateNodeStatus 更新节点状态
func (a *AgentEngine) updateNodeStatus() {
	a.nodeInfo.CurrentTasks = a.workerPool.ActiveWorkers()
	a.nodeInfo.CPUUsage = getCPUUsage()
	a.nodeInfo.MemoryUsage = getMemoryUsage()
	a.nodeInfo.LastHeartbeat = time.Now()
	a.nodeInfo.UpdatedTime = time.Now()
}

// GetNodeInfo 获取节点信息
func (a *AgentEngine) GetNodeInfo() *model.Node {
	return a.nodeInfo
}

// SubmitTask 提交任务
func (a *AgentEngine) SubmitTask(task *model.Task) error {
	select {
	case a.taskChan <- task:
		return nil
	default:
		return fmt.Errorf("task channel full")
	}
}
