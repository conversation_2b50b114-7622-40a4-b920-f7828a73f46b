package engine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golem-backend/pkg/config"
	"golem-backend/pkg/logger"
)

// CoreEngine 核心引擎实现
type CoreEngine struct {
	mu         sync.RWMutex
	config     *config.Config
	logger     logger.Logger
	components map[string]Component
	lifecycle  LifecycleManager
	registry   ServiceRegistry
	configMgr  ConfigManager
	pluginMgr  PluginManager
	eventBus   EventBus
	metrics    MetricsCollector
	started    bool
	ctx        context.Context
	cancel     context.CancelFunc
}

// NewCoreEngine 创建核心引擎
func NewCoreEngine(cfg *config.Config, log logger.Logger) *CoreEngine {
	ctx, cancel := context.WithCancel(context.Background())

	engine := &CoreEngine{
		config:     cfg,
		logger:     log,
		components: make(map[string]Component),
		ctx:        ctx,
		cancel:     cancel,
	}

	// 初始化内置组件
	engine.lifecycle = NewLifecycleManager(log)
	engine.configMgr = NewConfigManager(cfg, log)
	engine.eventBus = NewEventBus(log)
	engine.metrics = NewMetricsCollector(log)

	return engine
}

// Initialize 初始化引擎
func (e *CoreEngine) Initialize(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.logger.Info("Initializing core engine...")

	// 执行初始化前钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePreInit); err != nil {
		return fmt.Errorf("pre-init phase failed: %w", err)
	}

	// 按依赖顺序初始化组件
	components := e.sortComponentsByDependencies()
	for _, comp := range components {
		e.logger.Info(fmt.Sprintf("Initializing component: %s", comp.Name()))
		if err := comp.Initialize(ctx); err != nil {
			return fmt.Errorf("failed to initialize component %s: %w", comp.Name(), err)
		}
	}

	// 执行初始化后钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePostInit); err != nil {
		return fmt.Errorf("post-init phase failed: %w", err)
	}

	e.logger.Info("Core engine initialized successfully")
	return nil
}

// Start 启动引擎
func (e *CoreEngine) Start(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.started {
		return fmt.Errorf("engine already started")
	}

	e.logger.Info("Starting core engine...")

	// 执行启动前钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePreStart); err != nil {
		return fmt.Errorf("pre-start phase failed: %w", err)
	}

	// 按依赖顺序启动组件
	components := e.sortComponentsByDependencies()
	for _, comp := range components {
		e.logger.Info(fmt.Sprintf("Starting component: %s", comp.Name()))
		if err := comp.Start(ctx); err != nil {
			return fmt.Errorf("failed to start component %s: %w", comp.Name(), err)
		}
	}

	// 执行启动后钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePostStart); err != nil {
		return fmt.Errorf("post-start phase failed: %w", err)
	}

	e.started = true
	e.logger.Info("Core engine started successfully")

	// 启动健康检查
	go e.healthCheckLoop()

	return nil
}

// Stop 停止引擎
func (e *CoreEngine) Stop(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.started {
		return nil
	}

	e.logger.Info("Stopping core engine...")

	// 执行停止前钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePreStop); err != nil {
		e.logger.Error(fmt.Sprintf("pre-stop phase failed: %v", err))
	}

	// 按依赖顺序反向停止组件
	components := e.sortComponentsByDependencies()
	for i := len(components) - 1; i >= 0; i-- {
		comp := components[i]
		e.logger.Info(fmt.Sprintf("Stopping component: %s", comp.Name()))
		if err := comp.Stop(ctx); err != nil {
			e.logger.Error(fmt.Sprintf("failed to stop component %s: %v", comp.Name(), err))
		}
	}

	// 执行停止后钩子
	if err := e.lifecycle.ExecutePhase(ctx, PhasePostStop); err != nil {
		e.logger.Error(fmt.Sprintf("post-stop phase failed: %v", err))
	}

	e.started = false
	e.cancel()

	e.logger.Info("Core engine stopped")
	return nil
}

// Health 健康检查
func (e *CoreEngine) Health(ctx context.Context) error {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if !e.started {
		return fmt.Errorf("engine not started")
	}

	// 检查所有组件健康状态
	for name, comp := range e.components {
		if err := comp.Health(ctx); err != nil {
			return fmt.Errorf("component %s unhealthy: %w", name, err)
		}
	}

	return nil
}

// GetComponent 获取组件
func (e *CoreEngine) GetComponent(name string) (Component, error) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	comp, exists := e.components[name]
	if !exists {
		return nil, fmt.Errorf("component %s not found", name)
	}

	return comp, nil
}

// RegisterComponent 注册组件
func (e *CoreEngine) RegisterComponent(name string, component Component) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if _, exists := e.components[name]; exists {
		return fmt.Errorf("component %s already registered", name)
	}

	e.components[name] = component
	e.logger.Info(fmt.Sprintf("Component %s registered", name))

	return nil
}

// UnregisterComponent 注销组件
func (e *CoreEngine) UnregisterComponent(name string) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if _, exists := e.components[name]; !exists {
		return fmt.Errorf("component %s not found", name)
	}

	delete(e.components, name)
	e.logger.Info(fmt.Sprintf("Component %s unregistered", name))

	return nil
}

// sortComponentsByDependencies 按依赖关系排序组件
func (e *CoreEngine) sortComponentsByDependencies() []Component {
	var sorted []Component
	visited := make(map[string]bool)
	visiting := make(map[string]bool)

	var visit func(string) bool
	visit = func(name string) bool {
		if visiting[name] {
			// 检测到循环依赖
			e.logger.Error(fmt.Sprintf("Circular dependency detected for component: %s", name))
			return false
		}

		if visited[name] {
			return true
		}

		comp, exists := e.components[name]
		if !exists {
			return true
		}

		visiting[name] = true

		// 先访问依赖的组件
		for _, dep := range comp.Dependencies() {
			if !visit(dep) {
				return false
			}
		}

		visiting[name] = false
		visited[name] = true
		sorted = append(sorted, comp)

		return true
	}

	// 访问所有组件
	for name := range e.components {
		visit(name)
	}

	return sorted
}

// healthCheckLoop 健康检查循环
func (e *CoreEngine) healthCheckLoop() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(e.ctx, 10*time.Second)
			if err := e.Health(ctx); err != nil {
				e.logger.Error(fmt.Sprintf("Health check failed: %v", err))
				// 发布健康检查失败事件
				e.eventBus.Publish(ctx, "engine.health.failed", map[string]interface{}{
					"error": err.Error(),
					"time":  time.Now(),
				})
			}
			cancel()
		}
	}
}
