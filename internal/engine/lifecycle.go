package engine

import (
	"context"
	"fmt"
	"sync"

	"golem-backend/pkg/logger"
)

// lifecycleManager 生命周期管理器实现
type lifecycleManager struct {
	mu     sync.RWMutex
	hooks  map[LifecyclePhase][]LifecycleHook
	logger logger.Logger
}

// NewLifecycleManager 创建生命周期管理器
func NewLifecycleManager(log logger.Logger) LifecycleManager {
	return &lifecycleManager{
		hooks:  make(map[LifecyclePhase][]LifecycleHook),
		logger: log,
	}
}

// RegisterHook 注册生命周期钩子
func (lm *lifecycleManager) RegisterHook(phase LifecyclePhase, hook LifecycleHook) error {
	lm.mu.Lock()
	defer lm.mu.Unlock()
	
	if hook == nil {
		return fmt.Errorf("hook cannot be nil")
	}
	
	lm.hooks[phase] = append(lm.hooks[phase], hook)
	lm.logger.Debug(fmt.Sprintf("Registered hook for phase: %s", phase))
	
	return nil
}

// ExecutePhase 执行生命周期阶段
func (lm *lifecycleManager) ExecutePhase(ctx context.Context, phase LifecyclePhase) error {
	lm.mu.RLock()
	hooks := lm.hooks[phase]
	lm.mu.RUnlock()
	
	if len(hooks) == 0 {
		return nil
	}
	
	lm.logger.Debug(fmt.Sprintf("Executing %d hooks for phase: %s", len(hooks), phase))
	
	for i, hook := range hooks {
		if err := hook(ctx); err != nil {
			return fmt.Errorf("hook %d failed in phase %s: %w", i, phase, err)
		}
	}
	
	lm.logger.Debug(fmt.Sprintf("All hooks executed successfully for phase: %s", phase))
	return nil
}

// BaseComponent 基础组件实现
type BaseComponent struct {
	name         string
	dependencies []string
	logger       logger.Logger
	initialized  bool
	started      bool
	mu           sync.RWMutex
}

// NewBaseComponent 创建基础组件
func NewBaseComponent(name string, dependencies []string, log logger.Logger) *BaseComponent {
	return &BaseComponent{
		name:         name,
		dependencies: dependencies,
		logger:       log,
	}
}

// Name 组件名称
func (bc *BaseComponent) Name() string {
	return bc.name
}

// Dependencies 依赖的组件名称列表
func (bc *BaseComponent) Dependencies() []string {
	return bc.dependencies
}

// Initialize 初始化组件
func (bc *BaseComponent) Initialize(ctx context.Context) error {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	if bc.initialized {
		return nil
	}
	
	bc.logger.Debug(fmt.Sprintf("Initializing component: %s", bc.name))
	bc.initialized = true
	
	return nil
}

// Start 启动组件
func (bc *BaseComponent) Start(ctx context.Context) error {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	if !bc.initialized {
		return fmt.Errorf("component %s not initialized", bc.name)
	}
	
	if bc.started {
		return nil
	}
	
	bc.logger.Debug(fmt.Sprintf("Starting component: %s", bc.name))
	bc.started = true
	
	return nil
}

// Stop 停止组件
func (bc *BaseComponent) Stop(ctx context.Context) error {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	if !bc.started {
		return nil
	}
	
	bc.logger.Debug(fmt.Sprintf("Stopping component: %s", bc.name))
	bc.started = false
	
	return nil
}

// Health 健康检查
func (bc *BaseComponent) Health(ctx context.Context) error {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	
	if !bc.initialized || !bc.started {
		return fmt.Errorf("component %s not ready", bc.name)
	}
	
	return nil
}

// IsInitialized 检查是否已初始化
func (bc *BaseComponent) IsInitialized() bool {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	return bc.initialized
}

// IsStarted 检查是否已启动
func (bc *BaseComponent) IsStarted() bool {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	return bc.started
}

// ComponentGroup 组件组
type ComponentGroup struct {
	*BaseComponent
	components []Component
}

// NewComponentGroup 创建组件组
func NewComponentGroup(name string, dependencies []string, log logger.Logger) *ComponentGroup {
	return &ComponentGroup{
		BaseComponent: NewBaseComponent(name, dependencies, log),
		components:    make([]Component, 0),
	}
}

// AddComponent 添加组件到组
func (cg *ComponentGroup) AddComponent(component Component) {
	cg.components = append(cg.components, component)
}

// Initialize 初始化组件组
func (cg *ComponentGroup) Initialize(ctx context.Context) error {
	if err := cg.BaseComponent.Initialize(ctx); err != nil {
		return err
	}
	
	for _, comp := range cg.components {
		if err := comp.Initialize(ctx); err != nil {
			return fmt.Errorf("failed to initialize component %s in group %s: %w", 
				comp.Name(), cg.name, err)
		}
	}
	
	return nil
}

// Start 启动组件组
func (cg *ComponentGroup) Start(ctx context.Context) error {
	if err := cg.BaseComponent.Start(ctx); err != nil {
		return err
	}
	
	for _, comp := range cg.components {
		if err := comp.Start(ctx); err != nil {
			return fmt.Errorf("failed to start component %s in group %s: %w", 
				comp.Name(), cg.name, err)
		}
	}
	
	return nil
}

// Stop 停止组件组
func (cg *ComponentGroup) Stop(ctx context.Context) error {
	// 反向停止组件
	for i := len(cg.components) - 1; i >= 0; i-- {
		comp := cg.components[i]
		if err := comp.Stop(ctx); err != nil {
			cg.logger.Error(fmt.Sprintf("failed to stop component %s in group %s: %v", 
				comp.Name(), cg.name, err))
		}
	}
	
	return cg.BaseComponent.Stop(ctx)
}

// Health 健康检查组件组
func (cg *ComponentGroup) Health(ctx context.Context) error {
	if err := cg.BaseComponent.Health(ctx); err != nil {
		return err
	}
	
	for _, comp := range cg.components {
		if err := comp.Health(ctx); err != nil {
			return fmt.Errorf("component %s in group %s unhealthy: %w", 
				comp.Name(), cg.name, err)
		}
	}
	
	return nil
}

// GetComponents 获取组内所有组件
func (cg *ComponentGroup) GetComponents() []Component {
	return cg.components
}
