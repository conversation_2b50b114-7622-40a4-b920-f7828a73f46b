package engine

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"golem-backend/internal/model"
	"golem-backend/internal/scanner"
	"golem-backend/pkg/logger"
)

// Job 工作任务接口
type Job interface {
	Execute(ctx context.Context) error
	ID() string
}

// TaskJob 扫描任务工作
type TaskJob struct {
	task           *model.Task
	scannerManager scanner.ScannerManager
	logger         logger.Logger
}

// Execute 执行任务
func (tj *TaskJob) Execute(ctx context.Context) error {
	tj.logger.Info(fmt.Sprintf("Executing task: %s", tj.task.ID))

	// 更新任务状态为运行中
	tj.task.Status = model.TaskStatusRunning
	tj.task.StartTime = &[]time.Time{time.Now()}[0]
	tj.task.Progress = 0

	// 根据任务类型获取合适的扫描器
	selectedScanner, err := tj.scannerManager.GetScannerByTaskType(tj.task.Type)
	if err != nil {
		tj.logger.Error(fmt.Sprintf("Failed to get scanner for task type %s: %v", tj.task.Type, err))
		tj.task.Status = model.TaskStatusFailed
		tj.task.Error = err.Error()
		return err
	}

	tj.logger.Info(fmt.Sprintf("Using scanner: %s v%s for task: %s",
		selectedScanner.GetName(), selectedScanner.GetVersion(), tj.task.ID))

	// 执行扫描
	updatedTask, err := selectedScanner.Scan(ctx, tj.task)
	if err != nil {
		tj.logger.Error(fmt.Sprintf("Scanner execution failed for task %s: %v", tj.task.ID, err))
		tj.task.Status = model.TaskStatusFailed
		tj.task.Error = err.Error()
		return err
	}

	// 更新任务结果
	*tj.task = *updatedTask

	tj.logger.Info(fmt.Sprintf("Task completed successfully: %s", tj.task.ID))
	return nil
}

// ID 获取任务ID
func (tj *TaskJob) ID() string {
	return tj.task.ID
}



// WorkerPool 工作池
type WorkerPool struct {
	workerCount   int
	jobQueue      chan Job
	workers       []*Worker
	activeWorkers int64
	logger        logger.Logger
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
}

// NewWorkerPool 创建工作池
func NewWorkerPool(workerCount int, logger logger.Logger) *WorkerPool {
	return &WorkerPool{
		workerCount: workerCount,
		jobQueue:    make(chan Job, workerCount*2),
		workers:     make([]*Worker, workerCount),
		logger:      logger,
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start(ctx context.Context) {
	wp.ctx, wp.cancel = context.WithCancel(ctx)
	
	// 启动工作协程
	for i := 0; i < wp.workerCount; i++ {
		worker := NewWorker(i, wp.jobQueue, wp.logger)
		wp.workers[i] = worker
		
		wp.wg.Add(1)
		go func(w *Worker) {
			defer wp.wg.Done()
			w.Start(wp.ctx, wp)
		}(worker)
	}
	
	wp.logger.Info(fmt.Sprintf("Worker pool started with %d workers", wp.workerCount))
}

// Stop 停止工作池
func (wp *WorkerPool) Stop(ctx context.Context) {
	wp.logger.Info("Stopping worker pool...")
	
	// 关闭任务队列
	close(wp.jobQueue)
	
	// 取消上下文
	if wp.cancel != nil {
		wp.cancel()
	}
	
	// 等待所有工作协程完成
	wp.wg.Wait()
	
	wp.logger.Info("Worker pool stopped")
}

// Submit 提交任务
func (wp *WorkerPool) Submit(job Job) error {
	select {
	case wp.jobQueue <- job:
		return nil
	default:
		return fmt.Errorf("job queue full")
	}
}

// ActiveWorkers 获取活跃工作协程数
func (wp *WorkerPool) ActiveWorkers() int {
	return int(atomic.LoadInt64(&wp.activeWorkers))
}

// Worker 工作协程
type Worker struct {
	id     int
	jobs   <-chan Job
	logger logger.Logger
}

// NewWorker 创建工作协程
func NewWorker(id int, jobs <-chan Job, logger logger.Logger) *Worker {
	return &Worker{
		id:     id,
		jobs:   jobs,
		logger: logger,
	}
}

// Start 启动工作协程
func (w *Worker) Start(ctx context.Context, pool *WorkerPool) {
	w.logger.Debug(fmt.Sprintf("Worker %d started", w.id))
	
	for {
		select {
		case job, ok := <-w.jobs:
			if !ok {
				w.logger.Debug(fmt.Sprintf("Worker %d stopped", w.id))
				return
			}
			
			// 增加活跃工作协程计数
			atomic.AddInt64(&pool.activeWorkers, 1)
			
			// 执行任务
			w.logger.Debug(fmt.Sprintf("Worker %d executing job: %s", w.id, job.ID()))
			if err := job.Execute(ctx); err != nil {
				w.logger.Error(fmt.Sprintf("Worker %d job execution failed: %v", w.id, err))
			}
			
			// 减少活跃工作协程计数
			atomic.AddInt64(&pool.activeWorkers, -1)
			
		case <-ctx.Done():
			w.logger.Debug(fmt.Sprintf("Worker %d stopped due to context cancellation", w.id))
			return
		}
	}
}
