package engine

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"golem-backend/internal/model"
	"golem-backend/internal/scanner"
	"golem-backend/pkg/logger"
)

// Job 工作任务接口
type Job interface {
	Execute(ctx context.Context) error
	ID() string
}

// TaskJob 扫描任务工作
type TaskJob struct {
	task    *model.Task
	scanner scanner.Scanner
	logger  logger.Logger
}

// Execute 执行任务
func (tj *TaskJob) Execute(ctx context.Context) error {
	tj.logger.Info(fmt.Sprintf("Executing task: %s", tj.task.ID))
	
	// 更新任务状态为运行中
	tj.task.Status = model.TaskStatusRunning
	tj.task.StartTime = &[]time.Time{time.Now()}[0]
	tj.task.Progress = 0
	
	// 根据任务类型执行相应的扫描
	switch tj.task.Type {
	case model.TaskTypePortScan:
		return tj.executePortScan(ctx)
	case model.TaskTypeServiceDetect:
		return tj.executeServiceDetect(ctx)
	case model.TaskTypeVulnScan:
		return tj.executeVulnScan(ctx)
	case model.TaskTypeFingerprintScan:
		return tj.executeFingerprintScan(ctx)
	case model.TaskTypeAssetDiscovery:
		return tj.executeAssetDiscovery(ctx)
	default:
		return fmt.Errorf("unsupported task type: %s", tj.task.Type)
	}
}

// ID 获取任务ID
func (tj *TaskJob) ID() string {
	return tj.task.ID
}

// executePortScan 执行端口扫描
func (tj *TaskJob) executePortScan(ctx context.Context) error {
	tj.logger.Debug(fmt.Sprintf("Executing port scan for task: %s", tj.task.ID))
	
	// 模拟端口扫描过程
	for i := 0; i <= 100; i += 10 {
		select {
		case <-ctx.Done():
			tj.task.Status = model.TaskStatusCanceled
			return ctx.Err()
		default:
			time.Sleep(100 * time.Millisecond) // 模拟扫描时间
			tj.task.Progress = i
		}
	}
	
	// 设置扫描结果
	tj.task.Result = map[string]interface{}{
		"open_ports": []int{22, 80, 443, 8080},
		"scan_time":  time.Since(*tj.task.StartTime).Seconds(),
	}
	
	tj.task.Status = model.TaskStatusCompleted
	tj.task.Progress = 100
	completedTime := time.Now()
	tj.task.CompletedTime = &completedTime
	
	tj.logger.Info(fmt.Sprintf("Port scan completed for task: %s", tj.task.ID))
	return nil
}

// executeServiceDetect 执行服务识别
func (tj *TaskJob) executeServiceDetect(ctx context.Context) error {
	tj.logger.Debug(fmt.Sprintf("Executing service detection for task: %s", tj.task.ID))
	
	// 模拟服务识别过程
	for i := 0; i <= 100; i += 20 {
		select {
		case <-ctx.Done():
			tj.task.Status = model.TaskStatusCanceled
			return ctx.Err()
		default:
			time.Sleep(200 * time.Millisecond)
			tj.task.Progress = i
		}
	}
	
	// 设置识别结果
	tj.task.Result = map[string]interface{}{
		"services": []map[string]interface{}{
			{"port": 22, "service": "ssh", "version": "OpenSSH 8.0"},
			{"port": 80, "service": "http", "version": "nginx/1.18.0"},
			{"port": 443, "service": "https", "version": "nginx/1.18.0"},
		},
		"scan_time": time.Since(*tj.task.StartTime).Seconds(),
	}
	
	tj.task.Status = model.TaskStatusCompleted
	tj.task.Progress = 100
	completedTime := time.Now()
	tj.task.CompletedTime = &completedTime
	
	tj.logger.Info(fmt.Sprintf("Service detection completed for task: %s", tj.task.ID))
	return nil
}

// executeVulnScan 执行漏洞扫描
func (tj *TaskJob) executeVulnScan(ctx context.Context) error {
	tj.logger.Debug(fmt.Sprintf("Executing vulnerability scan for task: %s", tj.task.ID))
	
	// 模拟漏洞扫描过程
	for i := 0; i <= 100; i += 5 {
		select {
		case <-ctx.Done():
			tj.task.Status = model.TaskStatusCanceled
			return ctx.Err()
		default:
			time.Sleep(500 * time.Millisecond)
			tj.task.Progress = i
		}
	}
	
	// 设置扫描结果
	tj.task.Result = map[string]interface{}{
		"vulnerabilities": []map[string]interface{}{
			{"cve": "CVE-2021-44228", "severity": "critical", "description": "Log4j RCE"},
			{"cve": "CVE-2021-34527", "severity": "high", "description": "PrintNightmare"},
		},
		"scan_time": time.Since(*tj.task.StartTime).Seconds(),
	}
	
	tj.task.Status = model.TaskStatusCompleted
	tj.task.Progress = 100
	completedTime := time.Now()
	tj.task.CompletedTime = &completedTime
	
	tj.logger.Info(fmt.Sprintf("Vulnerability scan completed for task: %s", tj.task.ID))
	return nil
}

// executeFingerprintScan 执行指纹识别
func (tj *TaskJob) executeFingerprintScan(ctx context.Context) error {
	tj.logger.Debug(fmt.Sprintf("Executing fingerprint scan for task: %s", tj.task.ID))
	
	// 模拟指纹识别过程
	for i := 0; i <= 100; i += 25 {
		select {
		case <-ctx.Done():
			tj.task.Status = model.TaskStatusCanceled
			return ctx.Err()
		default:
			time.Sleep(300 * time.Millisecond)
			tj.task.Progress = i
		}
	}
	
	// 设置识别结果
	tj.task.Result = map[string]interface{}{
		"fingerprints": []map[string]interface{}{
			{"technology": "nginx", "version": "1.18.0", "confidence": 95},
			{"technology": "php", "version": "7.4", "confidence": 80},
			{"technology": "mysql", "version": "8.0", "confidence": 90},
		},
		"scan_time": time.Since(*tj.task.StartTime).Seconds(),
	}
	
	tj.task.Status = model.TaskStatusCompleted
	tj.task.Progress = 100
	completedTime := time.Now()
	tj.task.CompletedTime = &completedTime
	
	tj.logger.Info(fmt.Sprintf("Fingerprint scan completed for task: %s", tj.task.ID))
	return nil
}

// executeAssetDiscovery 执行资产发现
func (tj *TaskJob) executeAssetDiscovery(ctx context.Context) error {
	tj.logger.Debug(fmt.Sprintf("Executing asset discovery for task: %s", tj.task.ID))
	
	// 模拟资产发现过程
	for i := 0; i <= 100; i += 10 {
		select {
		case <-ctx.Done():
			tj.task.Status = model.TaskStatusCanceled
			return ctx.Err()
		default:
			time.Sleep(400 * time.Millisecond)
			tj.task.Progress = i
		}
	}
	
	// 设置发现结果
	tj.task.Result = map[string]interface{}{
		"assets": []map[string]interface{}{
			{"ip": "***********", "type": "router", "status": "active"},
			{"ip": "***********0", "type": "server", "status": "active"},
			{"ip": "************", "type": "workstation", "status": "active"},
		},
		"scan_time": time.Since(*tj.task.StartTime).Seconds(),
	}
	
	tj.task.Status = model.TaskStatusCompleted
	tj.task.Progress = 100
	completedTime := time.Now()
	tj.task.CompletedTime = &completedTime
	
	tj.logger.Info(fmt.Sprintf("Asset discovery completed for task: %s", tj.task.ID))
	return nil
}

// WorkerPool 工作池
type WorkerPool struct {
	workerCount   int
	jobQueue      chan Job
	workers       []*Worker
	activeWorkers int64
	logger        logger.Logger
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
}

// NewWorkerPool 创建工作池
func NewWorkerPool(workerCount int, logger logger.Logger) *WorkerPool {
	return &WorkerPool{
		workerCount: workerCount,
		jobQueue:    make(chan Job, workerCount*2),
		workers:     make([]*Worker, workerCount),
		logger:      logger,
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start(ctx context.Context) {
	wp.ctx, wp.cancel = context.WithCancel(ctx)
	
	// 启动工作协程
	for i := 0; i < wp.workerCount; i++ {
		worker := NewWorker(i, wp.jobQueue, wp.logger)
		wp.workers[i] = worker
		
		wp.wg.Add(1)
		go func(w *Worker) {
			defer wp.wg.Done()
			w.Start(wp.ctx, wp)
		}(worker)
	}
	
	wp.logger.Info(fmt.Sprintf("Worker pool started with %d workers", wp.workerCount))
}

// Stop 停止工作池
func (wp *WorkerPool) Stop(ctx context.Context) {
	wp.logger.Info("Stopping worker pool...")
	
	// 关闭任务队列
	close(wp.jobQueue)
	
	// 取消上下文
	if wp.cancel != nil {
		wp.cancel()
	}
	
	// 等待所有工作协程完成
	wp.wg.Wait()
	
	wp.logger.Info("Worker pool stopped")
}

// Submit 提交任务
func (wp *WorkerPool) Submit(job Job) error {
	select {
	case wp.jobQueue <- job:
		return nil
	default:
		return fmt.Errorf("job queue full")
	}
}

// ActiveWorkers 获取活跃工作协程数
func (wp *WorkerPool) ActiveWorkers() int {
	return int(atomic.LoadInt64(&wp.activeWorkers))
}

// Worker 工作协程
type Worker struct {
	id     int
	jobs   <-chan Job
	logger logger.Logger
}

// NewWorker 创建工作协程
func NewWorker(id int, jobs <-chan Job, logger logger.Logger) *Worker {
	return &Worker{
		id:     id,
		jobs:   jobs,
		logger: logger,
	}
}

// Start 启动工作协程
func (w *Worker) Start(ctx context.Context, pool *WorkerPool) {
	w.logger.Debug(fmt.Sprintf("Worker %d started", w.id))
	
	for {
		select {
		case job, ok := <-w.jobs:
			if !ok {
				w.logger.Debug(fmt.Sprintf("Worker %d stopped", w.id))
				return
			}
			
			// 增加活跃工作协程计数
			atomic.AddInt64(&pool.activeWorkers, 1)
			
			// 执行任务
			w.logger.Debug(fmt.Sprintf("Worker %d executing job: %s", w.id, job.ID()))
			if err := job.Execute(ctx); err != nil {
				w.logger.Error(fmt.Sprintf("Worker %d job execution failed: %v", w.id, err))
			}
			
			// 减少活跃工作协程计数
			atomic.AddInt64(&pool.activeWorkers, -1)
			
		case <-ctx.Done():
			w.logger.Debug(fmt.Sprintf("Worker %d stopped due to context cancellation", w.id))
			return
		}
	}
}
