package engine

import (
	"fmt"
	"sort"
	"sync"
	"sync/atomic"
	"time"

	"golem-backend/pkg/logger"
)

// metricsCollector 指标收集器实现
type metricsCollector struct {
	mu       sync.RWMutex
	counters map[string]*counter
	gauges   map[string]*gauge
	histos   map[string]*histogram
	timers   map[string]*timer
	logger   logger.Logger
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(log logger.Logger) MetricsCollector {
	return &metricsCollector{
		counters: make(map[string]*counter),
		gauges:   make(map[string]*gauge),
		histos:   make(map[string]*histogram),
		timers:   make(map[string]*timer),
		logger:   log,
	}
}

// Counter 获取计数器
func (mc *metricsCollector) Counter(name string, tags map[string]string) Counter {
	key := mc.buildKey(name, tags)
	
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	if c, exists := mc.counters[key]; exists {
		return c
	}
	
	c := &counter{name: name, tags: tags}
	mc.counters[key] = c
	
	return c
}

// Gauge 获取仪表盘
func (mc *metricsCollector) Gauge(name string, tags map[string]string) Gauge {
	key := mc.buildKey(name, tags)
	
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	if g, exists := mc.gauges[key]; exists {
		return g
	}
	
	g := &gauge{name: name, tags: tags}
	mc.gauges[key] = g
	
	return g
}

// Histogram 获取直方图
func (mc *metricsCollector) Histogram(name string, tags map[string]string) Histogram {
	key := mc.buildKey(name, tags)
	
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	if h, exists := mc.histos[key]; exists {
		return h
	}
	
	h := &histogram{
		name:    name,
		tags:    tags,
		buckets: make(map[float64]uint64),
	}
	mc.histos[key] = h
	
	return h
}

// Timer 获取计时器
func (mc *metricsCollector) Timer(name string, tags map[string]string) Timer {
	key := mc.buildKey(name, tags)
	
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	if t, exists := mc.timers[key]; exists {
		return t
	}
	
	t := &timer{
		name: name,
		tags: tags,
		hist: mc.Histogram(name+"_duration", tags).(*histogram),
	}
	mc.timers[key] = t
	
	return t
}

// buildKey 构建指标键
func (mc *metricsCollector) buildKey(name string, tags map[string]string) string {
	if len(tags) == 0 {
		return name
	}
	
	// 对标签进行排序以确保一致性
	keys := make([]string, 0, len(tags))
	for k := range tags {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	key := name
	for _, k := range keys {
		key += fmt.Sprintf(",%s=%s", k, tags[k])
	}
	
	return key
}

// GetAllMetrics 获取所有指标
func (mc *metricsCollector) GetAllMetrics() map[string]interface{} {
	mc.mu.RLock()
	defer mc.mu.RUnlock()
	
	metrics := make(map[string]interface{})
	
	// 收集计数器
	for key, counter := range mc.counters {
		metrics[key] = map[string]interface{}{
			"type":  "counter",
			"value": counter.Get(),
			"name":  counter.name,
			"tags":  counter.tags,
		}
	}
	
	// 收集仪表盘
	for key, gauge := range mc.gauges {
		metrics[key] = map[string]interface{}{
			"type":  "gauge",
			"value": gauge.Get(),
			"name":  gauge.name,
			"tags":  gauge.tags,
		}
	}
	
	// 收集直方图
	for key, histo := range mc.histos {
		metrics[key] = map[string]interface{}{
			"type":  "histogram",
			"count": histo.Count(),
			"sum":   histo.Sum(),
			"name":  histo.name,
			"tags":  histo.tags,
		}
	}
	
	return metrics
}

// counter 计数器实现
type counter struct {
	name  string
	tags  map[string]string
	value int64
}

func (c *counter) Inc(delta float64) {
	atomic.AddInt64(&c.value, int64(delta))
}

func (c *counter) Get() float64 {
	return float64(atomic.LoadInt64(&c.value))
}

// gauge 仪表盘实现
type gauge struct {
	name  string
	tags  map[string]string
	value int64
}

func (g *gauge) Set(value float64) {
	atomic.StoreInt64(&g.value, int64(value))
}

func (g *gauge) Get() float64 {
	return float64(atomic.LoadInt64(&g.value))
}

// histogram 直方图实现
type histogram struct {
	mu      sync.RWMutex
	name    string
	tags    map[string]string
	buckets map[float64]uint64
	count   uint64
	sum     float64
}

func (h *histogram) Observe(value float64) {
	h.mu.Lock()
	defer h.mu.Unlock()
	
	h.count++
	h.sum += value
	
	// 更新桶计数
	buckets := []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10}
	for _, bucket := range buckets {
		if value <= bucket {
			h.buckets[bucket]++
		}
	}
}

func (h *histogram) Count() uint64 {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.count
}

func (h *histogram) Sum() float64 {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.sum
}

// timer 计时器实现
type timer struct {
	name string
	tags map[string]string
	hist *histogram
}

func (t *timer) Start() func() {
	start := time.Now()
	return func() {
		duration := time.Since(start)
		t.Record(duration)
	}
}

func (t *timer) Record(duration time.Duration) {
	t.hist.Observe(duration.Seconds())
}

// MetricsSnapshot 指标快照
type MetricsSnapshot struct {
	Timestamp time.Time              `json:"timestamp"`
	Metrics   map[string]interface{} `json:"metrics"`
}

// TakeSnapshot 获取指标快照
func (mc *metricsCollector) TakeSnapshot() *MetricsSnapshot {
	return &MetricsSnapshot{
		Timestamp: time.Now(),
		Metrics:   mc.GetAllMetrics(),
	}
}

// Reset 重置所有指标
func (mc *metricsCollector) Reset() {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	mc.counters = make(map[string]*counter)
	mc.gauges = make(map[string]*gauge)
	mc.histos = make(map[string]*histogram)
	mc.timers = make(map[string]*timer)
	
	mc.logger.Info("Metrics collector reset")
}

// MetricsReporter 指标报告器
type MetricsReporter struct {
	collector MetricsCollector
	logger    logger.Logger
	interval  time.Duration
	stopCh    chan struct{}
}

// NewMetricsReporter 创建指标报告器
func NewMetricsReporter(collector MetricsCollector, logger logger.Logger, interval time.Duration) *MetricsReporter {
	return &MetricsReporter{
		collector: collector,
		logger:    logger,
		interval:  interval,
		stopCh:    make(chan struct{}),
	}
}

// Start 启动指标报告
func (mr *MetricsReporter) Start() {
	ticker := time.NewTicker(mr.interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			snapshot := mr.collector.(*metricsCollector).TakeSnapshot()
			mr.logger.Info(fmt.Sprintf("Metrics snapshot: %d metrics collected", len(snapshot.Metrics)))
			
			// 这里可以将指标发送到外部系统（如 Prometheus、InfluxDB 等）
			
		case <-mr.stopCh:
			return
		}
	}
}

// Stop 停止指标报告
func (mr *MetricsReporter) Stop() {
	close(mr.stopCh)
}
