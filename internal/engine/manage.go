package engine

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"golem-backend/internal/api/http/router"
	"golem-backend/internal/handler"
	"golem-backend/internal/repository"
	"golem-backend/internal/service"
	"golem-backend/pkg/cli"
	"golem-backend/pkg/config"
	"golem-backend/pkg/logger"
	"golem-backend/pkg/storage/cache/redis"
	"golem-backend/pkg/storage/database/mongodb"
	"golem-backend/pkg/storage/queue/rabbitmq"
)

// ManageEngine 管理平台引擎
type ManageEngine struct {
	*CoreEngine
	option *cli.Option
	config *config.Config
	logger logger.Logger

	// 存储组件
	mongodb  *mongodb.Client
	redis    *redis.Client
	rabbitmq *rabbitmq.Client

	// HTTP服务器
	httpServer *http.Server
	ginEngine  *gin.Engine

	// 业务组件
	repositories map[string]interface{}
	services     map[string]interface{}
	handlers     map[string]interface{}
}

// NewManageEngine 创建管理平台引擎
func NewManageEngine() (*ManageEngine, error) {
	// 解析命令行参数
	option := cli.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(option.ConfigFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 初始化日志
	log := logger.NewLogger(&cfg.Logger)

	// 创建核心引擎
	coreEngine := NewCoreEngine(cfg, log)

	engine := &ManageEngine{
		CoreEngine:   coreEngine,
		option:       option,
		config:       cfg,
		logger:       log,
		repositories: make(map[string]interface{}),
		services:     make(map[string]interface{}),
		handlers:     make(map[string]interface{}),
	}

	// 初始化存储组件
	if err := engine.initializeStorage(); err != nil {
		return nil, fmt.Errorf("failed to initialize storage: %w", err)
	}

	// 初始化业务组件
	if err := engine.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}

	// 初始化HTTP服务器
	if err := engine.initializeHTTPServer(); err != nil {
		return nil, fmt.Errorf("failed to initialize HTTP server: %w", err)
	}

	return engine, nil
}

// initializeStorage 初始化存储组件
func (m *ManageEngine) initializeStorage() error {
	var err error

	// 初始化MongoDB
	if m.mongodb, err = mongodb.NewClient(&m.config.Database); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %w", err)
	}

	// 初始化Redis
	if m.config.Redis.Enable {
		if m.redis, err = redis.NewClient(&m.config.Redis); err != nil {
			return fmt.Errorf("failed to initialize Redis: %w", err)
		}
	}

	// 初始化RabbitMQ
	if m.config.Rabbitmq.Enable {
		if m.rabbitmq, err = rabbitmq.NewClient(&m.config.Rabbitmq); err != nil {
			return fmt.Errorf("failed to initialize RabbitMQ: %w", err)
		}
	}

	m.logger.Info("Storage components initialized successfully")
	return nil
}

// initializeComponents 初始化业务组件
func (m *ManageEngine) initializeComponents() error {
	// 初始化仓库层
	m.initializeRepositories()

	// 初始化服务层
	m.initializeServices()

	// 初始化处理器层
	m.initializeHandlers()

	m.logger.Info("Business components initialized successfully")
	return nil
}

// initializeRepositories 初始化仓库层
func (m *ManageEngine) initializeRepositories() {
	// 验证码仓库
	if m.redis != nil {
		m.repositories["captcha"] = repository.NewCaptchaRepository(m.redis.Client)
	}

	// 用户仓库
	m.repositories["user"] = repository.NewUserRepository()

	// 资产仓库
	m.repositories["asset"] = repository.NewAssetRepository()

	// 节点仓库
	m.repositories["node"] = repository.NewNodeRepository()

	// 测绘仓库
	m.repositories["mapping"] = repository.NewMappingRepository()

	// 菜单仓库
	m.repositories["menu"] = repository.NewMenuRepository()

	// 许可证仓库
	m.repositories["license"] = repository.NewLicenseRepository()
}

// initializeServices 初始化服务层
func (m *ManageEngine) initializeServices() {
	// 验证码服务
	if captchaRepo, ok := m.repositories["captcha"].(repository.CaptchaRepository); ok {
		m.services["captcha"] = service.NewCaptchaService(captchaRepo)
	}

	// 用户服务
	if userRepo, ok := m.repositories["user"].(repository.UserRepository); ok {
		m.services["user"] = service.NewUserService(userRepo)
	}

	// // 资产服务
	// if assetRepo, ok := m.repositories["asset"].(repository.AssetRepository); ok {
	// 	m.services["asset"] = service.NewAssetService(assetRepo)
	// }
	//
	// // 节点服务
	// if nodeRepo, ok := m.repositories["node"].(repository.NodeRepository); ok {
	// 	m.services["node"] = service.NewNodeService(nodeRepo)
	// }
	//
	// // 测绘服务
	// if mappingRepo, ok := m.repositories["mapping"].(repository.MappingRepository); ok {
	// 	m.services["mapping"] = service.NewMappingService(mappingRepo)
	// }
}

// initializeHandlers 初始化处理器层
func (m *ManageEngine) initializeHandlers() {
	// 验证码处理器
	if captchaService, ok := m.services["captcha"].(service.CaptchaService); ok {
		m.handlers["captcha"] = handler.NewCaptchaHandler(captchaService)
	}

	// 用户处理器
	if userService, ok := m.services["user"].(service.UserService); ok {
		m.handlers["user"] = handler.NewUserHandler(userService)
	}

	// 健康检查处理器
	m.handlers["health"] = handler.NewHealthHandler()
}

// initializeHTTPServer 初始化HTTP服务器
func (m *ManageEngine) initializeHTTPServer() error {
	// 设置Gin模式
	if m.config.Application.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 创建Gin引擎
	m.ginEngine = gin.New()

	// 设置路由
	router.SetupManageRoutes(m.ginEngine)

	// 创建HTTP服务器
	m.httpServer = &http.Server{
		Addr:         ":" + strconv.Itoa(m.config.Application.Manage.Server.HTTPListenPort),
		Handler:      m.ginEngine,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	m.logger.Info(fmt.Sprintf("HTTP server configured on port %d",
		m.config.Application.Manage.Server.HTTPListenPort))

	return nil
}

// RunManageHttpServer 启动HTTP服务器
func (m *ManageEngine) RunManageHttpServer() error {
	m.logger.Info("Starting HTTP server...")

	go func() {
		if err := m.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			m.logger.Error(fmt.Sprintf("HTTP server error: %v", err))
		}
	}()

	m.logger.Info("HTTP server started successfully")
	return nil
}

// RunManageGrpcServer 启动gRPC服务器
func (m *ManageEngine) RunManageGrpcServer() error {
	m.logger.Info("gRPC server not implemented yet")
	return nil
}

// RunManageWebSocketServer 启动WebSocket服务器
func (m *ManageEngine) RunManageWebSocketServer() error {
	m.logger.Info("WebSocket server not implemented yet")
	return nil
}

// Shutdown 优雅关闭
func (m *ManageEngine) Shutdown(ctx context.Context) error {
	m.logger.Info("Shutting down manage engine...")

	// 关闭HTTP服务器
	if m.httpServer != nil {
		if err := m.httpServer.Shutdown(ctx); err != nil {
			m.logger.Error(fmt.Sprintf("HTTP server shutdown error: %v", err))
		}
	}

	// 关闭存储连接
	if m.mongodb != nil {
		if err := m.mongodb.Disconnect(ctx); err != nil {
			m.logger.Error(fmt.Sprintf("MongoDB disconnect error: %v", err))
		}
	}

	if m.redis != nil {
		if err := m.redis.Close(); err != nil {
			m.logger.Error(fmt.Sprintf("Redis close error: %v", err))
		}
	}

	if m.rabbitmq != nil {
		if err := m.rabbitmq.Close(); err != nil {
			m.logger.Error(fmt.Sprintf("RabbitMQ close error: %v", err))
		}
	}

	// 关闭核心引擎
	if err := m.CoreEngine.Stop(ctx); err != nil {
		m.logger.Error(fmt.Sprintf("Core engine stop error: %v", err))
	}

	m.logger.Info("Manage engine shutdown completed")
	return nil
}
