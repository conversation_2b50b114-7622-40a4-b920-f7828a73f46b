package engine

import (
	"github.com/gin-gonic/gin"
	"golem-backend/pkg/cli"
	"golem-backend/pkg/config"
	"golem-backend/pkg/logger"
	"golem-backend/pkg/storage/cache/redis"
	"golem-backend/pkg/storage/database/mongodb"
	"golem-backend/pkg/storage/queue/rabbitmq"
	"net/http"
)

// ManageEngine 管理平台引擎
type ManageEngine struct {
	option   *cli.Option
	config   *config.Config
	logger   *logger.Logger
	server   *http.Server
	handler  *gin.Engine
	mongodb  *mongodb.Client
	redis    *redis.Client
	rabbitmq *rabbitmq.Client
}

// NewManageEngine 初始化
func NewManageEngine() (*ManageEngine, error) {
	return nil, nil
}

func (m *ManageEngine) RunManageHttpServer() error {

	return nil
}

func (m *ManageEngine) RunManageGrpcServer() error {
	return nil
}

func (m *ManageEngine) RunManageWebSocketServer() error {
	return nil
}

func (m *ManageEngine) Stop() {}
