package engine

import (
	"context"
	"time"
)

// Engine 核心引擎接口
type Engine interface {
	// Initialize 初始化引擎
	Initialize(ctx context.Context) error
	
	// Start 启动引擎
	Start(ctx context.Context) error
	
	// Stop 停止引擎
	Stop(ctx context.Context) error
	
	// Health 健康检查
	Health(ctx context.Context) error
	
	// GetComponent 获取组件
	GetComponent(name string) (Component, error)
	
	// RegisterComponent 注册组件
	RegisterComponent(name string, component Component) error
	
	// UnregisterComponent 注销组件
	UnregisterComponent(name string) error
}

// Component 组件接口
type Component interface {
	// Name 组件名称
	Name() string
	
	// Initialize 初始化组件
	Initialize(ctx context.Context) error
	
	// Start 启动组件
	Start(ctx context.Context) error
	
	// Stop 停止组件
	Stop(ctx context.Context) error
	
	// Health 健康检查
	Health(ctx context.Context) error
	
	// Dependencies 依赖的组件名称列表
	Dependencies() []string
}

// LifecycleManager 生命周期管理器接口
type LifecycleManager interface {
	// RegisterHook 注册生命周期钩子
	RegisterHook(phase LifecyclePhase, hook LifecycleHook) error
	
	// ExecutePhase 执行生命周期阶段
	ExecutePhase(ctx context.Context, phase LifecyclePhase) error
}

// LifecyclePhase 生命周期阶段
type LifecyclePhase string

const (
	PhasePreInit    LifecyclePhase = "pre_init"    // 初始化前
	PhasePostInit   LifecyclePhase = "post_init"   // 初始化后
	PhasePreStart   LifecyclePhase = "pre_start"   // 启动前
	PhasePostStart  LifecyclePhase = "post_start"  // 启动后
	PhasePreStop    LifecyclePhase = "pre_stop"    // 停止前
	PhasePostStop   LifecyclePhase = "post_stop"   // 停止后
)

// LifecycleHook 生命周期钩子函数
type LifecycleHook func(ctx context.Context) error

// ServiceRegistry 服务注册中心接口
type ServiceRegistry interface {
	// Register 注册服务
	Register(ctx context.Context, service *ServiceInfo) error
	
	// Unregister 注销服务
	Unregister(ctx context.Context, serviceID string) error
	
	// Discover 发现服务
	Discover(ctx context.Context, serviceName string) ([]*ServiceInfo, error)
	
	// Watch 监听服务变化
	Watch(ctx context.Context, serviceName string) (<-chan []*ServiceInfo, error)
	
	// Health 健康检查
	Health(ctx context.Context, serviceID string) error
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Address  string            `json:"address"`
	Port     int               `json:"port"`
	Tags     []string          `json:"tags"`
	Meta     map[string]string `json:"meta"`
	Health   string            `json:"health"`
	LastSeen time.Time         `json:"last_seen"`
}

// ConfigManager 配置管理器接口
type ConfigManager interface {
	// Get 获取配置值
	Get(key string) interface{}
	
	// Set 设置配置值
	Set(key string, value interface{}) error
	
	// Watch 监听配置变化
	Watch(key string) (<-chan interface{}, error)
	
	// Reload 重新加载配置
	Reload(ctx context.Context) error
}

// PluginManager 插件管理器接口
type PluginManager interface {
	// LoadPlugin 加载插件
	LoadPlugin(ctx context.Context, pluginPath string) error
	
	// UnloadPlugin 卸载插件
	UnloadPlugin(ctx context.Context, pluginName string) error
	
	// GetPlugin 获取插件
	GetPlugin(pluginName string) (Plugin, error)
	
	// ListPlugins 列出所有插件
	ListPlugins() []string
}

// Plugin 插件接口
type Plugin interface {
	// Name 插件名称
	Name() string
	
	// Version 插件版本
	Version() string
	
	// Initialize 初始化插件
	Initialize(ctx context.Context, config map[string]interface{}) error
	
	// Execute 执行插件
	Execute(ctx context.Context, input interface{}) (interface{}, error)
	
	// Cleanup 清理插件
	Cleanup(ctx context.Context) error
}

// EventBus 事件总线接口
type EventBus interface {
	// Publish 发布事件
	Publish(ctx context.Context, topic string, event interface{}) error
	
	// Subscribe 订阅事件
	Subscribe(ctx context.Context, topic string, handler EventHandler) error
	
	// Unsubscribe 取消订阅
	Unsubscribe(ctx context.Context, topic string, handler EventHandler) error
}

// EventHandler 事件处理器
type EventHandler func(ctx context.Context, event interface{}) error

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	// Counter 计数器
	Counter(name string, tags map[string]string) Counter
	
	// Gauge 仪表盘
	Gauge(name string, tags map[string]string) Gauge
	
	// Histogram 直方图
	Histogram(name string, tags map[string]string) Histogram
	
	// Timer 计时器
	Timer(name string, tags map[string]string) Timer
}

// Counter 计数器
type Counter interface {
	Inc(delta float64)
	Get() float64
}

// Gauge 仪表盘
type Gauge interface {
	Set(value float64)
	Get() float64
}

// Histogram 直方图
type Histogram interface {
	Observe(value float64)
	Count() uint64
	Sum() float64
}

// Timer 计时器
type Timer interface {
	Start() func()
	Record(duration time.Duration)
}
