# Golem 核心引擎设计文档

## 1. 概述

Golem 核心引擎是整个网络攻击面监控系统的核心组件，负责管理系统的生命周期、依赖注入、配置管理、事件处理和服务协调。

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Golem 核心引擎                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 管理平台引擎 │  │ 代理节点引擎 │  │ 测绘服务引擎 │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    核心组件层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 生命周期管理 │  │ 配置管理器   │  │ 事件总线     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 服务注册中心 │  │ 插件管理器   │  │ 指标收集器   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   MongoDB   │  │    Redis    │  │  RabbitMQ   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 CoreEngine (核心引擎)
- **职责**: 系统初始化、组件管理、生命周期控制
- **功能**: 
  - 组件注册与发现
  - 依赖关系解析
  - 生命周期管理
  - 健康检查

#### 2.2.2 LifecycleManager (生命周期管理器)
- **职责**: 管理组件的生命周期钩子
- **功能**:
  - 钩子注册
  - 阶段执行
  - 错误处理

#### 2.2.3 ConfigManager (配置管理器)
- **职责**: 配置加载、监听、热重载
- **功能**:
  - 配置值获取/设置
  - 配置变化监听
  - 配置热重载

#### 2.2.4 EventBus (事件总线)
- **职责**: 系统内事件的发布与订阅
- **功能**:
  - 事件发布
  - 事件订阅
  - 异步处理

#### 2.2.5 MetricsCollector (指标收集器)
- **职责**: 系统指标收集与监控
- **功能**:
  - 计数器、仪表盘、直方图、计时器
  - 指标聚合
  - 指标导出

## 3. 引擎类型

### 3.1 ManageEngine (管理平台引擎)
- **用途**: 管理平台后端服务
- **组件**:
  - HTTP/gRPC/WebSocket 服务器
  - 业务服务层
  - 数据访问层
  - 存储组件

### 3.2 AgentEngine (代理节点引擎)
- **用途**: 分布式扫描节点
- **组件**:
  - 节点注册
  - 任务调度
  - 扫描执行
  - 工作池

### 3.3 MappingEngine (测绘服务引擎)
- **用途**: 资产测绘聚合服务
- **组件**:
  - 数据聚合
  - 查询接口
  - 缓存管理

## 4. 组件接口设计

### 4.1 Engine 接口
```go
type Engine interface {
    Initialize(ctx context.Context) error
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
    Health(ctx context.Context) error
    GetComponent(name string) (Component, error)
    RegisterComponent(name string, component Component) error
    UnregisterComponent(name string) error
}
```

### 4.2 Component 接口
```go
type Component interface {
    Name() string
    Initialize(ctx context.Context) error
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
    Health(ctx context.Context) error
    Dependencies() []string
}
```

## 5. 生命周期管理

### 5.1 生命周期阶段
1. **PreInit**: 初始化前
2. **PostInit**: 初始化后
3. **PreStart**: 启动前
4. **PostStart**: 启动后
5. **PreStop**: 停止前
6. **PostStop**: 停止后

### 5.2 组件启动顺序
1. 按依赖关系拓扑排序
2. 循环依赖检测
3. 并发启动无依赖组件
4. 错误回滚机制

## 6. 配置管理

### 6.1 配置来源
- 配置文件 (YAML)
- 环境变量
- 命令行参数
- 远程配置中心

### 6.2 配置热重载
- 文件监听
- 配置变化通知
- 组件配置更新

## 7. 事件系统

### 7.1 事件类型
- 系统事件 (启动、停止、错误)
- 业务事件 (任务创建、完成、失败)
- 监控事件 (指标变化、告警)

### 7.2 事件处理
- 异步处理
- 错误隔离
- 超时控制
- 重试机制

## 8. 指标监控

### 8.1 指标类型
- **Counter**: 计数器 (请求数、错误数)
- **Gauge**: 仪表盘 (CPU使用率、内存使用率)
- **Histogram**: 直方图 (响应时间分布)
- **Timer**: 计时器 (操作耗时)

### 8.2 指标导出
- Prometheus 格式
- JSON 格式
- 自定义格式

## 9. 错误处理

### 9.1 错误分类
- 系统错误 (配置错误、依赖错误)
- 业务错误 (数据验证错误、逻辑错误)
- 网络错误 (连接超时、网络中断)

### 9.2 错误恢复
- 自动重试
- 降级处理
- 故障转移
- 熔断机制

## 10. 扩展机制

### 10.1 插件系统
- 插件接口定义
- 动态加载/卸载
- 插件生命周期管理
- 插件间通信

### 10.2 组件扩展
- 自定义组件
- 组件继承
- 组件组合
- 组件代理

## 11. 安全考虑

### 11.1 访问控制
- 组件权限管理
- API 访问控制
- 资源隔离

### 11.2 数据安全
- 配置加密
- 通信加密
- 敏感数据脱敏

## 12. 性能优化

### 12.1 启动优化
- 并发初始化
- 懒加载
- 预热机制

### 12.2 运行时优化
- 连接池
- 缓存机制
- 批处理
- 异步处理

## 13. 部署架构

### 13.1 单机部署
```
┌─────────────────────────────────────┐
│            单机节点                  │
│  ┌─────────────┐  ┌─────────────┐   │
│  │ 管理平台引擎 │  │ 代理节点引擎 │   │
│  └─────────────┘  └─────────────┘   │
│  ┌─────────────────────────────────┐ │
│  │        存储组件                  │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 13.2 分布式部署
```
┌─────────────────┐    ┌─────────────────┐
│   管理平台节点   │    │   代理节点集群   │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 管理平台引擎 │ │    │ │ 代理节点引擎 │ │
│ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   存储集群       │
         │ ┌─────────────┐ │
         │ │   MongoDB   │ │
         │ │    Redis    │ │
         │ │  RabbitMQ   │ │
         │ └─────────────┘ │
         └─────────────────┘
```

## 14. 使用示例

### 14.1 创建自定义组件
```go
type CustomComponent struct {
    *engine.BaseComponent
    // 自定义字段
}

func (c *CustomComponent) Initialize(ctx context.Context) error {
    // 初始化逻辑
    return c.BaseComponent.Initialize(ctx)
}

func (c *CustomComponent) Start(ctx context.Context) error {
    // 启动逻辑
    return c.BaseComponent.Start(ctx)
}
```

### 14.2 注册组件
```go
engine := engine.NewCoreEngine(config, logger)
component := &CustomComponent{
    BaseComponent: engine.NewBaseComponent("custom", []string{"dependency"}, logger),
}
engine.RegisterComponent("custom", component)
```

### 14.3 生命周期钩子
```go
engine.RegisterHook(engine.PhasePreStart, func(ctx context.Context) error {
    // 启动前执行的逻辑
    return nil
})
```

## 15. 最佳实践

### 15.1 组件设计
- 单一职责原则
- 接口隔离原则
- 依赖倒置原则
- 开闭原则

### 15.2 错误处理
- 明确的错误类型
- 详细的错误信息
- 适当的错误恢复
- 错误日志记录

### 15.3 性能考虑
- 避免阻塞操作
- 合理使用缓存
- 控制并发数量
- 监控关键指标

### 15.4 可维护性
- 清晰的代码结构
- 完善的文档
- 充分的测试覆盖
- 版本兼容性
