# Golem 扫描器使用指南

## 概述

Golem 系统实现了三个专业扫描器模块，每个扫描器都针对特定的扫描任务进行了优化：

- **ZMap扫描器**: 大规模网络端口扫描
- **Nmap扫描器**: 多功能网络扫描（端口、服务、指纹识别）
- **Nuclei扫描器**: 漏洞扫描和安全检测

## 扫描器架构

### 接口设计

所有扫描器都实现了统一的 `Scanner` 接口：

```go
type Scanner interface {
    Scan(ctx context.Context, task *model.Task) (*model.Task, error)
    GetCapabilities() []string
    IsSupported(taskType model.TaskType) bool
    GetName() string
    GetVersion() string
}
```

### 工厂模式

使用工厂模式管理扫描器实例：

```go
// 创建扫描器工厂
factory := scanner.NewScannerFactory(logger)

// 根据任务类型获取最佳扫描器
scanner, err := factory.GetScannerByTaskType(model.TaskTypePortScan)
```

## ZMap扫描器

### 功能特点

- 支持大规模网络扫描
- 高性能端口扫描
- 可配置扫描速率和带宽限制
- 支持多种输出格式

### 配置选项

```go
zmapConfig := &scanner.ZMapConfig{
    ZMapPath:     "/usr/bin/zmap",
    MaxRate:      10000,        // 最大扫描速率 (packets/sec)
    Bandwidth:    "10M",        // 带宽限制
    CooldownTime: 8,            // 冷却时间 (seconds)
    Interface:    "eth0",       // 网络接口
    SourceIP:     "***********", // 源IP地址
}
```

### 使用示例

```go
// 创建ZMap扫描器
zmapScanner := scanner.NewZMapScanner(logger, zmapConfig)

// 创建扫描任务
task := &model.Task{
    ID:   "zmap_scan_001",
    Type: model.TaskTypePortScan,
    Config: map[string]interface{}{
        "target": "***********/24",
        "ports":  []int{80, 443, 22, 21},
        "max_rate": 5000,
        "bandwidth": "5M",
    },
}

// 执行扫描
result, err := zmapScanner.Scan(context.Background(), task)
if err != nil {
    log.Printf("Scan failed: %v", err)
    return
}

// 处理结果
scanResult := result.Result["scan_result"].(*scanner.ZMapScanResult)
fmt.Printf("Found %d open ports on %d hosts\n", 
    len(scanResult.OpenPorts), scanResult.AliveHosts)
```

### 结果格式

```json
{
  "total_hosts": 254,
  "alive_hosts": 12,
  "open_ports": [
    {
      "ip": "************",
      "port": 80,
      "protocol": "tcp",
      "state": "open"
    }
  ],
  "scan_duration": 45.2,
  "packets_sent": 1016,
  "packets_recv": 48,
  "scan_rate": 22.5
}
```

## Nmap扫描器

### 功能特点

- 多种扫描类型（TCP SYN、UDP、服务版本检测等）
- 服务识别和版本检测
- 操作系统指纹识别
- NSE脚本支持
- XML结果解析

### 配置选项

```go
nmapConfig := &scanner.NmapConfig{
    NmapPath:    "/usr/bin/nmap",
    MaxParallel: 100,           // 最大并行扫描数
    Timeout:     5 * time.Minute, // 扫描超时时间
    ScanType:    "syn",         // 扫描类型
    Timing:      "T3",          // 时序模板
    Scripts:     []string{"default", "discovery"}, // NSE脚本
}
```

### 扫描类型

- `syn`: TCP SYN扫描
- `tcp`: TCP连接扫描
- `udp`: UDP扫描
- `version`: 服务版本检测
- `script`: 脚本扫描
- `os`: 操作系统检测
- `aggressive`: 激进扫描

### 使用示例

```go
// 创建Nmap扫描器
nmapScanner := scanner.NewNmapScanner(logger, nmapConfig)

// 端口扫描任务
portScanTask := &model.Task{
    ID:   "nmap_port_scan_001",
    Type: model.TaskTypePortScan,
    Config: map[string]interface{}{
        "target": "************",
        "ports": []int{1, 65535}, // 端口范围
        "scan_type": "syn",
        "timing": "T4",
    },
}

// 服务识别任务
serviceScanTask := &model.Task{
    ID:   "nmap_service_scan_001",
    Type: model.TaskTypeServiceDetect,
    Config: map[string]interface{}{
        "target": "************",
        "ports": []int{80, 443, 22},
        "scan_type": "version",
    },
}

// 指纹识别任务
fingerprintTask := &model.Task{
    ID:   "nmap_fingerprint_001",
    Type: model.TaskTypeFingerprintScan,
    Config: map[string]interface{}{
        "target": "************",
        "scan_type": "script",
        "scripts": []string{"http-title", "ssh-hostkey"},
    },
}
```

### 结果格式

```json
{
  "hosts": [
    {
      "ip": "************",
      "hostname": "webserver.local",
      "status": "up",
      "ports": [
        {
          "port": 80,
          "protocol": "tcp",
          "state": "open",
          "service": {
            "name": "http",
            "product": "nginx",
            "version": "1.18.0",
            "extra_info": "Ubuntu"
          }
        }
      ],
      "os": {
        "name": "Linux 3.2 - 4.9",
        "accuracy": 95,
        "family": "Linux"
      }
    }
  ],
  "total_hosts": 1,
  "alive_hosts": 1,
  "scan_duration": 12.5
}
```

## Nuclei扫描器

### 功能特点

- 基于模板的漏洞扫描
- 支持自定义模板
- 多种严重性级别过滤
- 并发扫描控制
- JSON结果输出

### 配置选项

```go
nucleiConfig := &scanner.NucleiConfig{
    NucleiPath:     "/usr/bin/nuclei",
    TemplatesPath:  "/opt/nuclei-templates",
    MaxConcurrency: 25,         // 最大并发数
    Timeout:        10 * time.Minute, // 扫描超时
    RateLimit:      150,        // 速率限制 (requests/sec)
    Severity:       []string{"critical", "high"}, // 严重性过滤
    Tags:           []string{"cve", "rce"},       // 标签过滤
    ExcludeTags:    []string{"dos"},              // 排除标签
}
```

### 使用示例

```go
// 创建Nuclei扫描器
nucleiScanner := scanner.NewNucleiScanner(logger, nucleiConfig)

// 漏洞扫描任务
vulnScanTask := &model.Task{
    ID:   "nuclei_vuln_scan_001",
    Type: model.TaskTypeVulnScan,
    Config: map[string]interface{}{
        "target": "https://example.com",
        "severity": []string{"critical", "high", "medium"},
        "tags": []string{"cve", "rce", "sqli"},
        "exclude_tags": []string{"dos", "fuzz"},
        "max_concurrency": 10,
        "rate_limit": 100,
        "templates": []string{
            "cves/2021/CVE-2021-44228.yaml",
            "vulnerabilities/wordpress/",
        },
    },
}

// 执行扫描
result, err := nucleiScanner.Scan(context.Background(), vulnScanTask)
if err != nil {
    log.Printf("Scan failed: %v", err)
    return
}

// 处理结果
scanResult := result.Result["scan_result"].(*scanner.NucleiScanResult)
fmt.Printf("Found %d vulnerabilities\n", scanResult.TotalFindings)

// 按严重性分组
for severity, count := range scanResult.SeverityStats {
    fmt.Printf("%s: %d\n", severity, count)
}
```

### 结果格式

```json
{
  "vulnerabilities": [
    {
      "id": "CVE-2021-44228",
      "name": "Apache Log4j RCE",
      "description": "Apache Log4j2 Remote Code Execution",
      "severity": "critical",
      "tags": ["cve", "rce", "log4j"],
      "cve": ["CVE-2021-44228"],
      "cvss": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H",
      "cvss_score": 10.0,
      "host": "https://example.com",
      "url": "https://example.com/api/search",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ],
  "total_findings": 1,
  "severity_stats": {
    "critical": 1,
    "high": 0,
    "medium": 0,
    "low": 0
  }
}
```

## 扫描器管理

### 工厂模式使用

```go
// 创建扫描器工厂
factory := scanner.NewScannerFactory(logger)

// 列出所有扫描器
scanners := factory.ListScanners()
for _, s := range scanners {
    fmt.Printf("Scanner: %s v%s\n", s.GetName(), s.GetVersion())
    fmt.Printf("Capabilities: %v\n", s.GetCapabilities())
}

// 根据任务类型获取扫描器
portScanner, err := factory.GetScannerByTaskType(model.TaskTypePortScan)
vulnScanner, err := factory.GetScannerByTaskType(model.TaskTypeVulnScan)

// 获取扫描器统计信息
stats := factory.GetScannerStats()
fmt.Printf("Total scanners: %d\n", stats["total_scanners"])
```

### 可配置工厂

```go
// 创建配置
config := &scanner.ScannerConfig{
    ZMap: &scanner.ZMapConfig{
        ZMapPath: "/custom/zmap",
        MaxRate:  5000,
    },
    Nmap: &scanner.NmapConfig{
        NmapPath:    "/custom/nmap",
        MaxParallel: 50,
    },
    Nuclei: &scanner.NucleiConfig{
        NucleiPath:     "/custom/nuclei",
        TemplatesPath:  "/custom/templates",
        MaxConcurrency: 20,
    },
}

// 创建可配置工厂
factory := scanner.NewConfigurableFactory(logger, config)

// 更新配置
newConfig := &scanner.ScannerConfig{
    ZMap: &scanner.ZMapConfig{
        MaxRate: 10000,
    },
}
factory.UpdateConfig(newConfig)
```

## 最佳实践

### 1. 扫描器选择

- **大规模网络扫描**: 使用ZMap进行快速端口发现
- **详细主机扫描**: 使用Nmap进行服务识别和指纹识别
- **安全评估**: 使用Nuclei进行漏洞扫描

### 2. 性能优化

- 根据网络环境调整扫描速率
- 使用适当的并发数避免过载
- 设置合理的超时时间
- 使用时序模板控制扫描激进程度

### 3. 错误处理

```go
result, err := scanner.Scan(ctx, task)
if err != nil {
    // 检查是否是上下文取消
    if ctx.Err() == context.Canceled {
        log.Println("Scan was canceled")
        return
    }
    
    // 检查是否是超时
    if ctx.Err() == context.DeadlineExceeded {
        log.Println("Scan timed out")
        return
    }
    
    // 其他错误
    log.Printf("Scan failed: %v", err)
    return
}
```

### 4. 结果处理

```go
// 解析扫描结果
switch task.Type {
case model.TaskTypePortScan:
    if zmapResult, ok := result.Result["scan_result"].(*scanner.ZMapScanResult); ok {
        processPortScanResult(zmapResult)
    }
case model.TaskTypeVulnScan:
    if nucleiResult, ok := result.Result["scan_result"].(*scanner.NucleiScanResult); ok {
        processVulnScanResult(nucleiResult)
    }
}
```

### 5. 监控和日志

- 记录扫描开始和结束时间
- 监控扫描进度
- 记录错误和异常情况
- 统计扫描性能指标

## 扩展开发

### 自定义扫描器

```go
type CustomScanner struct {
    *scanner.BaseScanner
    // 自定义字段
}

func NewCustomScanner(log logger.Logger) *CustomScanner {
    capabilities := []string{"custom_scan"}
    return &CustomScanner{
        BaseScanner: scanner.NewBaseScanner("custom", "1.0.0", capabilities, log),
    }
}

func (c *CustomScanner) Scan(ctx context.Context, task *model.Task) (*model.Task, error) {
    // 实现自定义扫描逻辑
    return task, nil
}
```

### 注册自定义扫描器

```go
factory := scanner.NewScannerFactory(logger)
customScanner := NewCustomScanner(logger)
factory.RegisterScanner(customScanner)
```

## 故障排除

### 常见问题

1. **扫描器可执行文件未找到**
   - 检查扫描器路径配置
   - 确保可执行文件存在且有执行权限

2. **扫描超时**
   - 增加超时时间配置
   - 减少并发数或扫描速率

3. **权限不足**
   - 确保有足够权限执行网络扫描
   - 某些扫描类型需要root权限

4. **结果解析失败**
   - 检查扫描器输出格式
   - 验证XML/JSON解析逻辑

### 调试技巧

- 启用详细日志记录
- 使用小范围目标进行测试
- 检查网络连通性
- 验证扫描器版本兼容性
